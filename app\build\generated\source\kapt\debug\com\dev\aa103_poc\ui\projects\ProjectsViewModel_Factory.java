package com.dev.aa103_poc.ui.projects;

import com.dev.aa103_poc.data.preferences.UserPreferences;
import com.dev.aa103_poc.data.repository.ProjectRepositoryInterface;
import com.google.firebase.auth.FirebaseAuth;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProjectsViewModel_Factory implements Factory<ProjectsViewModel> {
  private final Provider<ProjectRepositoryInterface> repositoryProvider;

  private final Provider<FirebaseAuth> authProvider;

  private final Provider<UserPreferences> userPreferencesProvider;

  public ProjectsViewModel_Factory(Provider<ProjectRepositoryInterface> repositoryProvider,
      Provider<FirebaseAuth> authProvider, Provider<UserPreferences> userPreferencesProvider) {
    this.repositoryProvider = repositoryProvider;
    this.authProvider = authProvider;
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public ProjectsViewModel get() {
    return newInstance(repositoryProvider.get(), authProvider.get(), userPreferencesProvider.get());
  }

  public static ProjectsViewModel_Factory create(
      Provider<ProjectRepositoryInterface> repositoryProvider, Provider<FirebaseAuth> authProvider,
      Provider<UserPreferences> userPreferencesProvider) {
    return new ProjectsViewModel_Factory(repositoryProvider, authProvider, userPreferencesProvider);
  }

  public static ProjectsViewModel newInstance(ProjectRepositoryInterface repository,
      FirebaseAuth auth, UserPreferences userPreferences) {
    return new ProjectsViewModel(repository, auth, userPreferences);
  }
}
