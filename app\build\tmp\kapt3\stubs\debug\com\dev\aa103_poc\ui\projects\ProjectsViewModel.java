package com.dev.aa103_poc.ui.projects;

/**
 * ViewModel for the Projects screen
 * Handles manual loading and refreshing of project data
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0012\u001a\u00020\u0011J\u001a\u0010\u0013\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0002R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/dev/aa103_poc/ui/projects/ProjectsViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/dev/aa103_poc/data/repository/ProjectRepositoryInterface;", "auth", "Lcom/google/firebase/auth/FirebaseAuth;", "userPreferences", "Lcom/dev/aa103_poc/data/preferences/UserPreferences;", "(Lcom/dev/aa103_poc/data/repository/ProjectRepositoryInterface;Lcom/google/firebase/auth/FirebaseAuth;Lcom/dev/aa103_poc/data/preferences/UserPreferences;)V", "_state", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/dev/aa103_poc/ui/projects/ProjectsUiState;", "state", "Lkotlinx/coroutines/flow/StateFlow;", "getState", "()Lkotlinx/coroutines/flow/StateFlow;", "loadProjects", "", "refreshProjects", "updateStateFromProjectStream", "projectStream", "Lcom/dev/aa103_poc/data/model/ProjectStream;", "isRefreshing", "", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ProjectsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.dev.aa103_poc.data.repository.ProjectRepositoryInterface repository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.auth.FirebaseAuth auth = null;
    @org.jetbrains.annotations.NotNull()
    private final com.dev.aa103_poc.data.preferences.UserPreferences userPreferences = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.dev.aa103_poc.ui.projects.ProjectsUiState> _state = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.dev.aa103_poc.ui.projects.ProjectsUiState> state = null;
    
    @javax.inject.Inject()
    public ProjectsViewModel(@org.jetbrains.annotations.NotNull()
    com.dev.aa103_poc.data.repository.ProjectRepositoryInterface repository, @org.jetbrains.annotations.NotNull()
    com.google.firebase.auth.FirebaseAuth auth, @org.jetbrains.annotations.NotNull()
    com.dev.aa103_poc.data.preferences.UserPreferences userPreferences) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.dev.aa103_poc.ui.projects.ProjectsUiState> getState() {
        return null;
    }
    
    /**
     * Load projects from cache (called on initialization)
     * If this is the first login, also sync from server
     */
    public final void loadProjects() {
    }
    
    /**
     * Refresh projects from server (called by pull-to-refresh)
     */
    public final void refreshProjects() {
    }
    
    private final void updateStateFromProjectStream(com.dev.aa103_poc.data.model.ProjectStream projectStream, boolean isRefreshing) {
    }
}