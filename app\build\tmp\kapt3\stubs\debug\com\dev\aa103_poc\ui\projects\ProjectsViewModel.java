package com.dev.aa103_poc.ui.projects;

/**
 * ViewModel for the Projects screen
 * Handles streaming project data and managing UI state
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\b\u0010\u0010\u001a\u00020\u0011H\u0014J\u0006\u0010\u0012\u001a\u00020\u0011J\u0006\u0010\u0013\u001a\u00020\u0011R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/dev/aa103_poc/ui/projects/ProjectsViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/dev/aa103_poc/data/repository/ProjectRepositoryInterface;", "auth", "Lcom/google/firebase/auth/FirebaseAuth;", "(Lcom/dev/aa103_poc/data/repository/ProjectRepositoryInterface;Lcom/google/firebase/auth/FirebaseAuth;)V", "_state", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/dev/aa103_poc/ui/projects/ProjectsUiState;", "state", "Lkotlinx/coroutines/flow/StateFlow;", "getState", "()Lkotlinx/coroutines/flow/StateFlow;", "streamingJob", "Lkotlinx/coroutines/Job;", "onCleared", "", "startStreaming", "stopStreaming", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ProjectsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.dev.aa103_poc.data.repository.ProjectRepositoryInterface repository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.firebase.auth.FirebaseAuth auth = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.dev.aa103_poc.ui.projects.ProjectsUiState> _state = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.dev.aa103_poc.ui.projects.ProjectsUiState> state = null;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job streamingJob;
    
    @javax.inject.Inject()
    public ProjectsViewModel(@org.jetbrains.annotations.NotNull()
    com.dev.aa103_poc.data.repository.ProjectRepositoryInterface repository, @org.jetbrains.annotations.NotNull()
    com.google.firebase.auth.FirebaseAuth auth) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.dev.aa103_poc.ui.projects.ProjectsUiState> getState() {
        return null;
    }
    
    /**
     * Start streaming projects for the current user
     * This should be called from UI with proper lifecycle management
     */
    public final void startStreaming() {
    }
    
    /**
     * Stop streaming projects
     * Called automatically when ViewModel is cleared
     */
    public final void stopStreaming() {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
}