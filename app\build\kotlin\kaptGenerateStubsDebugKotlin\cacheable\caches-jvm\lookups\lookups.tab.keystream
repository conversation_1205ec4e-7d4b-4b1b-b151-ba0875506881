  Activity android.app  Application android.app  Bundle android.app.Activity  Intent android.content  Bundle android.content.Context  Bundle android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Log android.util  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Add &androidx.compose.material.icons.filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  Button androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FloatingActionButton androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  ListItem androidx.compose.material3  
MaterialTheme androidx.compose.material3  OutlinedTextField androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  DisposableEffect androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  Boolean androidx.lifecycle.ViewModel  CreateProjectUiState androidx.lifecycle.ViewModel  Firebase androidx.lifecycle.ViewModel  FirebaseAuth androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  ProjectRepository androidx.lifecycle.ViewModel  ProjectRepositoryInterface androidx.lifecycle.ViewModel  ProjectsUiState androidx.lifecycle.ViewModel  
SignInUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  auth androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  AA103Application com.dev.aa103_poc  MainActivity com.dev.aa103_poc  
MainScreen com.dev.aa103_poc  MainScreenPreview com.dev.aa103_poc  Bundle com.dev.aa103_poc.MainActivity  Boolean com.dev.aa103_poc.data.model  List com.dev.aa103_poc.data.model  Project com.dev.aa103_poc.data.model  
ProjectStream com.dev.aa103_poc.data.model  String com.dev.aa103_poc.data.model  List $com.dev.aa103_poc.data.model.Project  String $com.dev.aa103_poc.data.model.Project  	Timestamp $com.dev.aa103_poc.data.model.Project  List .com.dev.aa103_poc.data.model.Project.Companion  String .com.dev.aa103_poc.data.model.Project.Companion  	Timestamp .com.dev.aa103_poc.data.model.Project.Companion  Boolean *com.dev.aa103_poc.data.model.ProjectStream  List *com.dev.aa103_poc.data.model.ProjectStream  Project *com.dev.aa103_poc.data.model.ProjectStream  FirebaseAuth !com.dev.aa103_poc.data.repository  FirebaseFirestore !com.dev.aa103_poc.data.repository  FirestoreProjectRepository !com.dev.aa103_poc.data.repository  IllegalStateException !com.dev.aa103_poc.data.repository  List !com.dev.aa103_poc.data.repository  ProjectRepository !com.dev.aa103_poc.data.repository  ProjectRepositoryInterface !com.dev.aa103_poc.data.repository  String !com.dev.aa103_poc.data.repository  let !com.dev.aa103_poc.data.repository  FirebaseFirestore <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  Flow <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  Inject <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  
ProjectStream <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  String <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  FirebaseAuth 3com.dev.aa103_poc.data.repository.ProjectRepository  FirebaseFirestore 3com.dev.aa103_poc.data.repository.ProjectRepository  IllegalStateException 3com.dev.aa103_poc.data.repository.ProjectRepository  List 3com.dev.aa103_poc.data.repository.ProjectRepository  Project 3com.dev.aa103_poc.data.repository.ProjectRepository  String 3com.dev.aa103_poc.data.repository.ProjectRepository  auth 3com.dev.aa103_poc.data.repository.ProjectRepository  	firestore 3com.dev.aa103_poc.data.repository.ProjectRepository  getLET 3com.dev.aa103_poc.data.repository.ProjectRepository  getLet 3com.dev.aa103_poc.data.repository.ProjectRepository  let 3com.dev.aa103_poc.data.repository.ProjectRepository  Flow <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  
ProjectStream <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  String <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  	AppModule com.dev.aa103_poc.di  RepositoryModule com.dev.aa103_poc.di  SingletonComponent com.dev.aa103_poc.di  FirebaseAuth com.dev.aa103_poc.di.AppModule  FirebaseFirestore com.dev.aa103_poc.di.AppModule  Provides com.dev.aa103_poc.di.AppModule  	Singleton com.dev.aa103_poc.di.AppModule  Binds %com.dev.aa103_poc.di.RepositoryModule  FirestoreProjectRepository %com.dev.aa103_poc.di.RepositoryModule  ProjectRepositoryInterface %com.dev.aa103_poc.di.RepositoryModule  	Singleton %com.dev.aa103_poc.di.RepositoryModule  AuthGate com.dev.aa103_poc.ui.auth  Unit com.dev.aa103_poc.ui.auth  Boolean "com.dev.aa103_poc.ui.createproject  
Composable "com.dev.aa103_poc.ui.createproject  CreateProjectActivity "com.dev.aa103_poc.ui.createproject  CreateProjectScreen "com.dev.aa103_poc.ui.createproject  CreateProjectScreenPreview "com.dev.aa103_poc.ui.createproject  CreateProjectUiState "com.dev.aa103_poc.ui.createproject  CreateProjectViewModel "com.dev.aa103_poc.ui.createproject  ExperimentalMaterial3Api "com.dev.aa103_poc.ui.createproject  JobTypeDropdown "com.dev.aa103_poc.ui.createproject  MutableStateFlow "com.dev.aa103_poc.ui.createproject  OptIn "com.dev.aa103_poc.ui.createproject  ProjectTypeDropdown "com.dev.aa103_poc.ui.createproject  String "com.dev.aa103_poc.ui.createproject  Unit "com.dev.aa103_poc.ui.createproject  asStateFlow "com.dev.aa103_poc.ui.createproject  Bundle 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  Boolean 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  String 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  Boolean 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  CreateProjectUiState 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  MutableStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  ProjectRepository 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  	StateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  String 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  _uiState 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  asStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getASStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getAsStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  Boolean Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  CreateProjectUiState Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  MutableStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  ProjectRepository Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  	StateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  String Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  asStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  getASStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  getAsStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  Boolean com.dev.aa103_poc.ui.projects  
EmptyProjects com.dev.aa103_poc.ui.projects  ExperimentalMaterial3Api com.dev.aa103_poc.ui.projects  List com.dev.aa103_poc.ui.projects  MutableStateFlow com.dev.aa103_poc.ui.projects  OptIn com.dev.aa103_poc.ui.projects  ProjectsList com.dev.aa103_poc.ui.projects  ProjectsScreen com.dev.aa103_poc.ui.projects  ProjectsScreenPreview com.dev.aa103_poc.ui.projects  ProjectsUiState com.dev.aa103_poc.ui.projects  ProjectsViewModel com.dev.aa103_poc.ui.projects  String com.dev.aa103_poc.ui.projects  Unit com.dev.aa103_poc.ui.projects  Boolean -com.dev.aa103_poc.ui.projects.ProjectsUiState  List -com.dev.aa103_poc.ui.projects.ProjectsUiState  Loading -com.dev.aa103_poc.ui.projects.ProjectsUiState  Project -com.dev.aa103_poc.ui.projects.ProjectsUiState  ProjectsUiState -com.dev.aa103_poc.ui.projects.ProjectsUiState  String -com.dev.aa103_poc.ui.projects.ProjectsUiState  Boolean 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  List 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  Project 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  String 3com.dev.aa103_poc.ui.projects.ProjectsUiState.Error  FirebaseAuth /com.dev.aa103_poc.ui.projects.ProjectsViewModel  Inject /com.dev.aa103_poc.ui.projects.ProjectsViewModel  MutableStateFlow /com.dev.aa103_poc.ui.projects.ProjectsViewModel  ProjectRepositoryInterface /com.dev.aa103_poc.ui.projects.ProjectsViewModel  ProjectsUiState /com.dev.aa103_poc.ui.projects.ProjectsViewModel  	StateFlow /com.dev.aa103_poc.ui.projects.ProjectsViewModel  _state /com.dev.aa103_poc.ui.projects.ProjectsViewModel  Boolean com.dev.aa103_poc.ui.signin  Firebase com.dev.aa103_poc.ui.signin  MutableStateFlow com.dev.aa103_poc.ui.signin  SignInActivity com.dev.aa103_poc.ui.signin  
SignInContent com.dev.aa103_poc.ui.signin  SignInScreen com.dev.aa103_poc.ui.signin  SignInScreenPreview com.dev.aa103_poc.ui.signin  
SignInUiState com.dev.aa103_poc.ui.signin  SignInViewModel com.dev.aa103_poc.ui.signin  String com.dev.aa103_poc.ui.signin  Unit com.dev.aa103_poc.ui.signin  asStateFlow com.dev.aa103_poc.ui.signin  Bundle *com.dev.aa103_poc.ui.signin.SignInActivity  Boolean )com.dev.aa103_poc.ui.signin.SignInUiState  String )com.dev.aa103_poc.ui.signin.SignInUiState  Boolean +com.dev.aa103_poc.ui.signin.SignInViewModel  Firebase +com.dev.aa103_poc.ui.signin.SignInViewModel  FirebaseAuth +com.dev.aa103_poc.ui.signin.SignInViewModel  MutableStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
SignInUiState +com.dev.aa103_poc.ui.signin.SignInViewModel  	StateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  String +com.dev.aa103_poc.ui.signin.SignInViewModel  _uiState +com.dev.aa103_poc.ui.signin.SignInViewModel  asStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  auth +com.dev.aa103_poc.ui.signin.SignInViewModel  getASStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  getAsStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  Boolean 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  Firebase 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  FirebaseAuth 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  MutableStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  
SignInUiState 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  	StateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  String 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  asStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  auth 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  getASStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  getAsStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  AA103_POCTheme com.dev.aa103_poc.ui.theme  Boolean com.dev.aa103_poc.ui.theme  DarkColorScheme com.dev.aa103_poc.ui.theme  LightColorScheme com.dev.aa103_poc.ui.theme  Pink40 com.dev.aa103_poc.ui.theme  Pink80 com.dev.aa103_poc.ui.theme  Purple40 com.dev.aa103_poc.ui.theme  Purple80 com.dev.aa103_poc.ui.theme  PurpleGrey40 com.dev.aa103_poc.ui.theme  PurpleGrey80 com.dev.aa103_poc.ui.theme  
Typography com.dev.aa103_poc.ui.theme  Unit com.dev.aa103_poc.ui.theme  	Timestamp com.google.firebase  FirebaseAuth com.google.firebase.auth  currentUser %com.google.firebase.auth.FirebaseAuth  getCURRENTUser %com.google.firebase.auth.FirebaseAuth  getCurrentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  setCurrentUser %com.google.firebase.auth.FirebaseAuth  getUID %com.google.firebase.auth.FirebaseUser  getUid %com.google.firebase.auth.FirebaseUser  setUid %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  auth com.google.firebase.auth.ktx  CollectionReference com.google.firebase.firestore  
FieldValue com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  Query com.google.firebase.firestore  document 1com.google.firebase.firestore.CollectionReference  
collection /com.google.firebase.firestore.DocumentReference  
collection /com.google.firebase.firestore.FirebaseFirestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  document #com.google.firebase.firestore.Query  Firebase com.google.firebase.ktx  auth  com.google.firebase.ktx.Firebase  getAUTH  com.google.firebase.ktx.Firebase  getAuth  com.google.firebase.ktx.Firebase  Binds dagger  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  SingletonComponent dagger.hilt.components  CreateProjectUiState 	java.lang  ExperimentalMaterial3Api 	java.lang  Firebase 	java.lang  FirebaseAuth 	java.lang  FirebaseFirestore 	java.lang  IllegalStateException 	java.lang  MutableStateFlow 	java.lang  ProjectsUiState 	java.lang  
SignInUiState 	java.lang  SingletonComponent 	java.lang  asStateFlow 	java.lang  let 	java.lang  Inject javax.inject  	Singleton javax.inject  Boolean kotlin  CreateProjectUiState kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Firebase kotlin  FirebaseAuth kotlin  FirebaseFirestore kotlin  	Function1 kotlin  IllegalStateException kotlin  Int kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  ProjectsUiState kotlin  
SignInUiState kotlin  SingletonComponent kotlin  String kotlin  Unit kotlin  asStateFlow kotlin  let kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  getLET 
kotlin.String  getLet 
kotlin.String  CreateProjectUiState kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  Firebase kotlin.annotation  FirebaseAuth kotlin.annotation  FirebaseFirestore kotlin.annotation  IllegalStateException kotlin.annotation  MutableStateFlow kotlin.annotation  ProjectsUiState kotlin.annotation  
SignInUiState kotlin.annotation  SingletonComponent kotlin.annotation  asStateFlow kotlin.annotation  let kotlin.annotation  CreateProjectUiState kotlin.collections  ExperimentalMaterial3Api kotlin.collections  Firebase kotlin.collections  FirebaseAuth kotlin.collections  FirebaseFirestore kotlin.collections  IllegalStateException kotlin.collections  List kotlin.collections  MutableStateFlow kotlin.collections  ProjectsUiState kotlin.collections  
SignInUiState kotlin.collections  SingletonComponent kotlin.collections  asStateFlow kotlin.collections  let kotlin.collections  CreateProjectUiState kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  Firebase kotlin.comparisons  FirebaseAuth kotlin.comparisons  FirebaseFirestore kotlin.comparisons  IllegalStateException kotlin.comparisons  MutableStateFlow kotlin.comparisons  ProjectsUiState kotlin.comparisons  
SignInUiState kotlin.comparisons  SingletonComponent kotlin.comparisons  asStateFlow kotlin.comparisons  let kotlin.comparisons  CreateProjectUiState 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  Firebase 	kotlin.io  FirebaseAuth 	kotlin.io  FirebaseFirestore 	kotlin.io  IllegalStateException 	kotlin.io  MutableStateFlow 	kotlin.io  ProjectsUiState 	kotlin.io  
SignInUiState 	kotlin.io  SingletonComponent 	kotlin.io  asStateFlow 	kotlin.io  let 	kotlin.io  CreateProjectUiState 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  Firebase 
kotlin.jvm  FirebaseAuth 
kotlin.jvm  FirebaseFirestore 
kotlin.jvm  IllegalStateException 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  ProjectsUiState 
kotlin.jvm  
SignInUiState 
kotlin.jvm  SingletonComponent 
kotlin.jvm  asStateFlow 
kotlin.jvm  let 
kotlin.jvm  CreateProjectUiState 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  Firebase 
kotlin.ranges  FirebaseAuth 
kotlin.ranges  FirebaseFirestore 
kotlin.ranges  IllegalStateException 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  ProjectsUiState 
kotlin.ranges  
SignInUiState 
kotlin.ranges  SingletonComponent 
kotlin.ranges  asStateFlow 
kotlin.ranges  let 
kotlin.ranges  KClass kotlin.reflect  CreateProjectUiState kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  Firebase kotlin.sequences  FirebaseAuth kotlin.sequences  FirebaseFirestore kotlin.sequences  IllegalStateException kotlin.sequences  MutableStateFlow kotlin.sequences  ProjectsUiState kotlin.sequences  
SignInUiState kotlin.sequences  SingletonComponent kotlin.sequences  asStateFlow kotlin.sequences  let kotlin.sequences  CreateProjectUiState kotlin.text  ExperimentalMaterial3Api kotlin.text  Firebase kotlin.text  FirebaseAuth kotlin.text  FirebaseFirestore kotlin.text  IllegalStateException kotlin.text  MutableStateFlow kotlin.text  ProjectsUiState kotlin.text  
SignInUiState kotlin.text  SingletonComponent kotlin.text  asStateFlow kotlin.text  let kotlin.text  launch kotlinx.coroutines  
awaitClose kotlinx.coroutines.channels  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  await kotlinx.coroutines.tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           