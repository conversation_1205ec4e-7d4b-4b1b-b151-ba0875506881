{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-3:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\149a3b4ba2574ca8a09717a816387d40\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "92,95", "startColumns": "4,4", "startOffsets": "5893,6017", "endColumns": "53,66", "endOffsets": "5942,6079"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "86", "endOffsets": "138"}, "to": {"startLines": "260", "startColumns": "4", "startOffsets": "18080", "endColumns": "85", "endOffsets": "18161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\a4bea82b7593c3f98dbaf8d2853bd0f4\\transformed\\activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "96,114", "startColumns": "4,4", "startOffsets": "6084,7028", "endColumns": "41,59", "endOffsets": "6121,7083"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,136,240,348,468,569", "endColumns": "80,103,107,119,100,69", "endOffsets": "131,235,343,463,564,634"}, "to": {"startLines": "158,159,160,161,162,229", "startColumns": "4,4,4,4,4,4", "startOffsets": "11150,11231,11335,11443,11563,16329", "endColumns": "80,103,107,119,100,69", "endOffsets": "11226,11330,11438,11558,11659,16394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6fa2c1a7648815a2389f1e233b858239\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,177,178,179,180,181,182,183,184,185,186,187,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11776,11864,11950,12031,12115,12184,12249,12332,12438,12524,12644,12698,12767,12828,12897,12986,13081,13155,13252,13345,13443,13592,13683,13771,13867,13965,14029,14097,14184,14278,14345,14417,14489,14590,14699,14775,14844,14892,14958,15022,15079,15136,15208,15258,15312,15383,15454,15524,15593,15651,15727,15798,15872,15958,16008,16078", "endLines": "165,166,167,168,169,170,171,172,173,176,177,178,179,180,181,182,183,184,185,186,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "11859,11945,12026,12110,12179,12244,12327,12433,12519,12639,12693,12762,12823,12892,12981,13076,13150,13247,13340,13438,13587,13678,13766,13862,13960,14024,14092,14179,14273,14340,14412,14484,14585,14694,14770,14839,14887,14953,15017,15074,15131,15203,15253,15307,15378,15449,15519,15588,15646,15722,15793,15867,15953,16003,16073,16138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c08faea6ded0ab196312fa217ce48810\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "120,121", "startColumns": "4,4", "startOffsets": "7383,7465", "endColumns": "81,83", "endOffsets": "7460,7544"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\481238e97b83b818931a2722abe3cf6f\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "237,238", "startColumns": "4,4", "startOffsets": "16793,16849", "endColumns": "55,54", "endOffsets": "16844,16899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,3,4,10,11,20,21,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,119,124,125,126,127,128,129,130,233,249,250,254,255,259,265,266,267,273,283,316,337,370", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,661,726,1463,1532,2011,2081,2149,2221,2291,2352,2426,2499,2560,2621,2683,2747,2809,2870,2938,3038,3098,3164,3237,3306,3363,3415,3477,3549,3625,5947,5982,6126,6181,6244,6299,6357,6415,6476,6539,6596,6647,6697,6758,6815,6881,6915,6950,7313,7679,7746,7818,7887,7956,8030,8102,16549,17322,17439,17640,17750,17951,18389,18461,18528,18731,19032,20763,21444,22126", "endLines": "2,3,4,10,11,20,21,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,119,124,125,126,127,128,129,130,233,249,253,254,258,259,265,266,272,282,315,336,369,375", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,277,365,721,787,1527,1590,2076,2144,2216,2286,2347,2421,2494,2555,2616,2678,2742,2804,2865,2933,3033,3093,3159,3232,3301,3358,3410,3472,3544,3620,3685,5977,6012,6176,6239,6294,6352,6410,6471,6534,6591,6642,6692,6753,6810,6876,6910,6945,6980,7378,7741,7813,7882,7951,8025,8097,8185,16615,17434,17635,17745,17946,18075,18456,18523,18726,19027,20758,21439,22121,22288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e262177e497b967ef733f0fbd8d750b5\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "6,7,8,9,28,29,151,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "412,470,536,599,1868,1939,10642,10935,11002,11081", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "465,531,594,656,1934,2006,10705,10997,11076,11145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\799b421d1a1acf7bdf6b855ee1ea22a8\\transformed\\play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "12,13,14,15,16,17,18,19,133,134,135,136,137,138,139,140,142,143,144,145,146,147,148,149,150,376,389", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,882,962,1052,1142,1222,1303,1383,8309,8414,8595,8720,8827,9007,9130,9246,9516,9704,9809,9990,10115,10290,10438,10501,10563,22293,22608", "endLines": "12,13,14,15,16,17,18,19,133,134,135,136,137,138,139,140,142,143,144,145,146,147,148,149,150,388,407", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "877,957,1047,1137,1217,1298,1378,1458,8409,8590,8715,8822,9002,9125,9241,9344,9699,9804,9985,10110,10285,10433,10496,10558,10637,22603,23020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\186e6ee20ce18e0ea2d0d0d836a5c219\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "7088", "endColumns": "53", "endOffsets": "7137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc3f110c8971a71c5a78795c57b75cdc\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "118,141", "startColumns": "4,4", "startOffsets": "7245,9349", "endColumns": "67,166", "endOffsets": "7308,9511"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "5,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "370,1595,1642,1689,1736,1781,1826", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "407,1637,1684,1731,1776,1821,1863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e2d099b519fdc69439ea38afa004a592\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "261", "startColumns": "4", "startOffsets": "18166", "endLines": "264", "endColumns": "12", "endOffsets": "18384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc19fa474d2af57c4563efc2f3a37b9f\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "7142", "endColumns": "49", "endOffsets": "7187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,117,131,132,152,153,154,163,164,225,226,227,228,230,231,232,234,235,236,239,242,245", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3690,3749,3808,3868,3928,3988,4048,4108,4168,4228,4288,4348,4408,4467,4527,4587,4647,4707,4767,4827,4887,4947,5007,5067,5126,5186,5246,5305,5364,5423,5482,5541,5600,5674,5732,5787,5838,7192,8190,8255,10710,10776,10877,11664,11716,16143,16205,16259,16295,16399,16449,16503,16620,16667,16703,16904,17016,17127", "endLines": "55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,117,131,132,152,153,154,163,164,225,226,227,228,230,231,232,234,235,236,241,244,248", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "3744,3803,3863,3923,3983,4043,4103,4163,4223,4283,4343,4403,4462,4522,4582,4642,4702,4762,4822,4882,4942,5002,5062,5121,5181,5241,5300,5359,5418,5477,5536,5595,5669,5727,5782,5833,5888,7240,8250,8304,10771,10872,10930,11711,11771,16200,16254,16290,16324,16444,16498,16544,16662,16698,16788,17011,17122,17317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8a74b0e4f3bb98071cef54851052374b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "122", "startColumns": "4", "startOffsets": "7549", "endColumns": "82", "endOffsets": "7627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\1cccaf25635db5690db81a5bd84de368\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "113", "startColumns": "4", "startOffsets": "6985", "endColumns": "42", "endOffsets": "7023"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "46", "endOffsets": "58"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7632", "endColumns": "46", "endOffsets": "7674"}}]}]}