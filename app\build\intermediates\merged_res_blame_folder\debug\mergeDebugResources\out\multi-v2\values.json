{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-3:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7737a164322ddcfaf310e17c3ae3dd04\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "58,59,60,61,80,81,208,212,213,214", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2020,2078,2144,2207,3476,3547,12549,12842,12909,12988", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2073,2139,2202,2264,3542,3614,12612,12904,12983,13052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\48127b829acf9e3a2697ec3974b843b9\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,141,142,144,145,174,188,189,209,210,211,220,221,282,283,284,285,287,288,289,291,292,293,296,299,302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5298,5357,5416,5476,5536,5596,5656,5716,5776,5836,5896,5956,6016,6075,6135,6195,6255,6315,6375,6435,6495,6555,6615,6675,6734,6794,6854,6913,6972,7031,7090,7149,7208,7342,7400,7512,7563,9099,10097,10162,12617,12683,12784,13571,13623,18050,18112,18166,18202,18306,18356,18410,18527,18574,18610,18811,18923,19034", "endLines": "107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,141,142,144,145,174,188,189,209,210,211,220,221,282,283,284,285,287,288,289,291,292,293,298,301,305", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "5352,5411,5471,5531,5591,5651,5711,5771,5831,5891,5951,6011,6070,6130,6190,6250,6310,6370,6430,6490,6550,6610,6670,6729,6789,6849,6908,6967,7026,7085,7144,7203,7277,7395,7450,7558,7613,9147,10157,10211,12678,12779,12837,13618,13678,18107,18161,18197,18231,18351,18405,18451,18569,18605,18695,18918,19029,19224"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "86", "endOffsets": "138"}, "to": {"startLines": "317", "startColumns": "4", "startOffsets": "19987", "endColumns": "85", "endOffsets": "20068"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,136,240,348,468,569", "endColumns": "80,103,107,119,100,69", "endOffsets": "131,235,343,463,564,634"}, "to": {"startLines": "215,216,217,218,219,286", "startColumns": "4,4,4,4,4,4", "startOffsets": "13057,13138,13242,13350,13470,18236", "endColumns": "80,103,107,119,100,69", "endOffsets": "13133,13237,13345,13465,13566,18301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e237bb86748d298953d7258fba5c001c\\transformed\\window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,140,324,337,531,539,554", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,382,601,880,1194,1382,1569,1622,1682,1734,1779,7282,20435,20934,27583,27865,28479", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,140,329,341,538,553,569", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "204,377,596,815,1189,1377,1564,1617,1677,1729,1774,1813,7337,20625,21087,27860,28474,29128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\603084b8eccd7975e4dd2fad567213a0\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "171", "startColumns": "4", "startOffsets": "8931", "endColumns": "53", "endOffsets": "8980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5bea5b7a692e33546695f4eda025ecd3\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "179", "startColumns": "4", "startOffsets": "9456", "endColumns": "82", "endOffsets": "9534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5229e2862d6364ca32e3a3bb311a3e6f\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "175,198", "startColumns": "4,4", "startOffsets": "9152,11256", "endColumns": "67,166", "endOffsets": "9215,11418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\050cc1ef6f2a2d973bccca489c96cbdc\\transformed\\navigation-common-2.4.0\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "473,486,492,498,507", "startColumns": "4,4,4,4,4", "startOffsets": "25491,26130,26374,26621,26984", "endLines": "485,491,497,500,511", "endColumns": "24,24,24,24,24", "endOffsets": "26125,26369,26616,26749,27161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c3003d5cffc968eefc6e7a108e33ffd4\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "146,150", "startColumns": "4,4", "startOffsets": "7618,7795", "endColumns": "53,66", "endOffsets": "7667,7857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9745ba946685dbad84077bac085b6d0d\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "172", "startColumns": "4", "startOffsets": "8985", "endColumns": "49", "endOffsets": "9030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d3a7801405f4d72120d05d7dea20d496\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "177,178", "startColumns": "4,4", "startOffsets": "9290,9372", "endColumns": "81,83", "endOffsets": "9367,9451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e95a4dc8fc9a2a597ef4366ce41e3822\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "318", "startColumns": "4", "startOffsets": "20073", "endLines": "321", "endColumns": "12", "endOffsets": "20291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be2279520d6cb9b749def7b903120ca1\\transformed\\play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "64,65,66,67,68,69,70,71,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,460,512", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2400,2490,2570,2660,2750,2830,2911,2991,10216,10321,10502,10627,10734,10914,11037,11153,11423,11611,11716,11897,12022,12197,12345,12408,12470,25176,27166", "endLines": "64,65,66,67,68,69,70,71,190,191,192,193,194,195,196,197,199,200,201,202,203,204,205,206,207,472,530", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2485,2565,2655,2745,2825,2906,2986,3066,10316,10497,10622,10729,10909,11032,11148,11251,11606,11711,11892,12017,12192,12340,12403,12465,12544,25486,27578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fd22806efa89fd4e339f9b2dc02f092a\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "143,152,173,412,417", "startColumns": "4,4,4,4,4", "startOffsets": "7455,7904,9035,24008,24178", "endLines": "143,152,173,416,420", "endColumns": "56,64,63,24,24", "endOffsets": "7507,7964,9094,24173,24322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6ea528116884d6f2cb33292a8ad9e376\\transformed\\activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "151,170", "startColumns": "4,4", "startOffsets": "7862,8871", "endColumns": "41,59", "endOffsets": "7899,8926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\b143239b82cf782ddd1a03f79809b6d9\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "169", "startColumns": "4", "startOffsets": "8828", "endColumns": "42", "endOffsets": "8866"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "57,74,75,76,77,78,79", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1978,3203,3250,3297,3344,3389,3434", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2015,3245,3292,3339,3384,3429,3471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\64d1623d530d67b7029efe1f2f178a6f\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "294,295", "startColumns": "4,4", "startOffsets": "18700,18756", "endColumns": "55,54", "endOffsets": "18751,18806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\cefdc79400af01ee96d40a4907365265\\transformed\\navigation-runtime-2.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "149,330,501,504", "startColumns": "4,4,4,4", "startOffsets": "7742,20630,26754,26869", "endLines": "149,336,503,506", "endColumns": "52,24,24,24", "endOffsets": "7790,20929,26864,26979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\abd85caff3ff8eef039557ffb8e6d4a5\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,62,63,72,73,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,147,148,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,176,181,182,183,184,185,186,187,290,306,307,311,312,316,322,323,342,348,358,391,421,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,1818,1890,2269,2334,3071,3140,3619,3689,3757,3829,3899,3960,4034,4107,4168,4229,4291,4355,4417,4478,4546,4646,4706,4772,4845,4914,4971,5023,5085,5157,5233,7672,7707,7969,8024,8087,8142,8200,8258,8319,8382,8439,8490,8540,8601,8658,8724,8758,8793,9220,9586,9653,9725,9794,9863,9937,10009,18456,19229,19346,19547,19657,19858,20296,20368,21092,21295,21596,23327,24327,25009", "endLines": "25,55,56,62,63,72,73,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,147,148,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,176,181,182,183,184,185,186,187,290,306,310,311,315,316,322,323,347,357,390,411,453,459", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "875,1885,1973,2329,2395,3135,3198,3684,3752,3824,3894,3955,4029,4102,4163,4224,4286,4350,4412,4473,4541,4641,4701,4767,4840,4909,4966,5018,5080,5152,5228,5293,7702,7737,8019,8082,8137,8195,8253,8314,8377,8434,8485,8535,8596,8653,8719,8753,8788,8823,9285,9648,9720,9789,9858,9932,10004,10092,18522,19341,19542,19652,19853,19982,20363,20430,21290,21591,23322,24003,25004,25171"}}, {"source": "C:\\Users\\<USER>\\Github\\AA103_POC\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "46", "endOffsets": "58"}, "to": {"startLines": "180", "startColumns": "4", "startOffsets": "9539", "endColumns": "46", "endOffsets": "9581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4bccd5606ba1f722682e9c63c366f5bf\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "222,223,224,225,226,227,228,229,230,231,234,235,236,237,238,239,240,241,242,243,244,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13683,13771,13857,13938,14022,14091,14156,14239,14345,14431,14551,14605,14674,14735,14804,14893,14988,15062,15159,15252,15350,15499,15590,15678,15774,15872,15936,16004,16091,16185,16252,16324,16396,16497,16606,16682,16751,16799,16865,16929,16986,17043,17115,17165,17219,17290,17361,17431,17500,17558,17634,17705,17779,17865,17915,17985", "endLines": "222,223,224,225,226,227,228,229,230,233,234,235,236,237,238,239,240,241,242,243,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "13766,13852,13933,14017,14086,14151,14234,14340,14426,14546,14600,14669,14730,14799,14888,14983,15057,15154,15247,15345,15494,15585,15673,15769,15867,15931,15999,16086,16180,16247,16319,16391,16492,16601,16677,16746,16794,16860,16924,16981,17038,17110,17160,17214,17285,17356,17426,17495,17553,17629,17700,17774,17860,17910,17980,18045"}}]}]}