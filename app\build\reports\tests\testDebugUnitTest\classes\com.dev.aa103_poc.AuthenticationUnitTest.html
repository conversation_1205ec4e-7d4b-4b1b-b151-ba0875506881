<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.dev.aa103_poc.AuthenticationUnitTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.dev.aa103_poc.AuthenticationUnitTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.dev.aa103_poc.html">com.dev.aa103_poc</a> &gt; AuthenticationUnitTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">9</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.045s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">emailValidation_invalidEmail_returnsFalse</td>
<td class="success">0.035s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">emailValidation_validEmail_returnsTrue</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">signInUiState_initialState_isCorrect</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">signInUiState_togglePasswordVisibility_updatesCorrectly</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">signInUiState_withEmail_updatesCorrectly</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">signInUiState_withErrorMessage_updatesCorrectly</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">signInUiState_withLoadingState_updatesCorrectly</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">signInUiState_withPassword_updatesCorrectly</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">signInUiState_withSignedInState_updatesCorrectly</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.7</a> at Sep 2, 2025, 8:14:11 PM</p>
</div>
</div>
</body>
</html>
