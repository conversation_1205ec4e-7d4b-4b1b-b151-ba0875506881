{"schemaVersion": "1.1.0", "buildSystem": "<PERSON><PERSON><PERSON>", "buildSystemVersion": "8.7", "buildPlugin": "org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper", "buildPluginVersion": "1.9.24", "projectSettings": {"isHmppEnabled": true, "isCompatibilityMetadataVariantEnabled": false, "isKPMEnabled": false}, "projectTargets": [{"target": "org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget", "platformType": "androidJvm", "extras": {"android": {"sourceCompatibility": "11", "targetCompatibility": "11"}}}]}