package com.dev.aa103_poc.ui.projects;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u000b\u001a\u00020\fH\u0007J\b\u0010\r\u001a\u00020\fH\u0007J\f\u0010\u000e\u001a\u00060\fj\u0002`\u000fH\u0007J\f\u0010\u0010\u001a\u00060\fj\u0002`\u000fH\u0007J\f\u0010\u0011\u001a\u00060\fj\u0002`\u000fH\u0007R\u0012\u0010\u0003\u001a\u00020\u00048\u0002@\u0002X\u0083.\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u0005\u001a\u00020\u00068\u0002@\u0002X\u0083.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\t\u001a\u00020\n8\u0002@\u0002X\u0083.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/dev/aa103_poc/ui/projects/ProjectsViewModelTest;", "", "()V", "auth", "Lcom/google/firebase/auth/FirebaseAuth;", "repository", "Lcom/dev/aa103_poc/data/repository/ProjectRepositoryInterface;", "testDispatcher", "Lkotlinx/coroutines/test/TestDispatcher;", "user", "Lcom/google/firebase/auth/FirebaseUser;", "setup", "", "tearDown", "when user has no projects, should show empty state", "Lkotlinx/coroutines/test/TestResult;", "when user has projects, should show content state", "when user is not signed in, should show error state", "app_releaseUnitTest"})
@kotlin.OptIn(markerClass = {kotlinx.coroutines.ExperimentalCoroutinesApi.class})
public final class ProjectsViewModelTest {
    @org.mockito.Mock()
    private com.dev.aa103_poc.data.repository.ProjectRepositoryInterface repository;
    @org.mockito.Mock()
    private com.google.firebase.auth.FirebaseAuth auth;
    @org.mockito.Mock()
    private com.google.firebase.auth.FirebaseUser user;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.test.TestDispatcher testDispatcher = null;
    
    public ProjectsViewModelTest() {
        super();
    }
    
    @org.junit.Before()
    public final void setup() {
    }
    
    @org.junit.After()
    public final void tearDown() {
    }
}