package com.dev.aa103_poc.ui.projects

import android.content.Intent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.Button
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.dev.aa103_poc.ui.createproject.CreateProjectActivity
import com.dev.aa103_poc.ui.theme.AA103_POCTheme
import com.google.firebase.auth.FirebaseAuth

@Composable
fun ProjectsScreen(
    modifier: Modifier = Modifier,
    onSignOut: () -> Unit = {}
) {
    val context = LocalContext.current

    Box(modifier = modifier.fillMaxSize()) {
        // Main content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Projects",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = "Welcome! You are successfully signed in.",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // Show current user email if available
            FirebaseAuth.getInstance().currentUser?.email?.let { email ->
                Text(
                    text = "Signed in as: $email",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(bottom = 32.dp)
                )
            }

            Button(
                onClick = onSignOut
            ) {
                Text("Sign Out")
            }
        }

        // Floating Action Button positioned at bottom right with increased padding
        FloatingActionButton(
            onClick = {
                val intent = Intent(context, CreateProjectActivity::class.java)
                context.startActivity(intent)
            },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(start = 24.dp, end = 24.dp, bottom = 48.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "Create new project"
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ProjectsScreenPreview() {
    AA103_POCTheme {
        ProjectsScreen()
    }
}
