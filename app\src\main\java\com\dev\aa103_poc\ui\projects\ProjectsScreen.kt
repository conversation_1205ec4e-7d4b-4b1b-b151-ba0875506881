package com.dev.aa103_poc.ui.projects

import android.content.Intent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshContainer
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.dev.aa103_poc.data.model.Project
import com.dev.aa103_poc.ui.components.LoadingIndicator
import com.dev.aa103_poc.ui.createproject.CreateProjectActivity
import com.dev.aa103_poc.ui.theme.AA103_POCTheme

/**
 * Route for Projects screen
 * No longer needs lifecycle management since we use manual refresh
 */
@Composable
fun ProjectsRoute(
    viewModel: ProjectsViewModel = hiltViewModel()
) {
    ProjectsScreen(viewModel = viewModel)
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProjectsScreen(
    viewModel: ProjectsViewModel
) {
    val state by viewModel.state.collectAsState()
    val context = LocalContext.current

    val pullToRefreshState = rememberPullToRefreshState()
    var isUserRefreshing by remember { mutableStateOf(false) }

    // Handle pull-to-refresh
    LaunchedEffect(pullToRefreshState.isRefreshing) {
        if (pullToRefreshState.isRefreshing && !isUserRefreshing) {
            isUserRefreshing = true
            viewModel.refreshProjects()
        }
    }

    // Update pull-to-refresh state based on ViewModel state
    LaunchedEffect(state, isUserRefreshing) {
        if (isUserRefreshing) {
            when (val currentState = state) {
                is ProjectsUiState.Content -> {
                    if (!currentState.isRefreshing) {
                        pullToRefreshState.endRefresh()
                        isUserRefreshing = false
                    }
                }
                is ProjectsUiState.Empty,
                is ProjectsUiState.Error -> {
                    // End refresh for empty or error states
                    pullToRefreshState.endRefresh()
                    isUserRefreshing = false
                }
                ProjectsUiState.Loading -> {
                    // Don't end refresh during loading, wait for content/empty/error
                }
            }
        }
    }

    Scaffold(
        floatingActionButton = {
            FloatingActionButton(
                onClick = {
                    val intent = Intent(context, CreateProjectActivity::class.java)
                    context.startActivity(intent)
                }
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Create new project"
                )
            }
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .padding(padding)
                .fillMaxSize()
                .nestedScroll(pullToRefreshState.nestedScrollConnection)
        ) {
            when (val currentState = state) {
                ProjectsUiState.Loading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                is ProjectsUiState.Error -> {
                    Text(
                        text = currentState.message,
                        modifier = Modifier.align(Alignment.Center),
                        color = MaterialTheme.colorScheme.error
                    )
                }
                ProjectsUiState.Empty -> {
                    EmptyProjects(
                        modifier = Modifier.align(Alignment.Center),
                        onAdd = {
                            val intent = Intent(context, CreateProjectActivity::class.java)
                            context.startActivity(intent)
                        }
                    )
                }
                is ProjectsUiState.Content -> {
                    ProjectsList(projects = currentState.projects)
                }
            }

            // Pull-to-refresh indicator
            PullToRefreshContainer(
                state = pullToRefreshState,
                modifier = Modifier.align(Alignment.TopCenter)
            )
        }
    }
}

@Composable
private fun EmptyProjects(
    modifier: Modifier = Modifier,
    onAdd: () -> Unit
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "No projects yet",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "Create your first project to get started",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = onAdd) {
            Text("Create your first project")
        }
    }
}

@Composable
private fun ProjectsList(projects: List<Project>) {
    LazyColumn {
        items(projects, key = { it.id }) { project ->
            ListItem(
                headlineContent = { Text(project.title) },
                supportingContent = { Text("${project.type} • ${project.jobType}") }
            )
            HorizontalDivider()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ProjectsScreenPreview() {
    AA103_POCTheme {
        // Note: Preview won't work with Hilt ViewModel
        // ProjectsScreen()
    }
}
