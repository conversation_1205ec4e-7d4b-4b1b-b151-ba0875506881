package com.dev.aa103_poc.data.preferences

import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages user preferences for the application
 */
@Singleton
class UserPreferences @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val prefs: SharedPreferences = context.getSharedPreferences(
        "user_preferences", 
        Context.MODE_PRIVATE
    )

    /**
     * Check if this is the first login for a specific user
     * @param uid User ID
     * @return true if first login, false otherwise
     */
    fun isFirstLogin(uid: String): Boolean {
        return !prefs.getBoolean("has_synced_$uid", false)
    }

    /**
     * Mark that the user has completed their first sync
     * @param uid User ID
     */
    fun markFirstSyncComplete(uid: String) {
        prefs.edit()
            .putBoolean("has_synced_$uid", true)
            .apply()
    }

    /**
     * Clear preferences for a user (e.g., on logout)
     * @param uid User ID
     */
    fun clearUserPreferences(uid: String) {
        prefs.edit()
            .remove("has_synced_$uid")
            .apply()
    }
}
