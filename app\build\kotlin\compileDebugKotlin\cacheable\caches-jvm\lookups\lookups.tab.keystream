  Activity android.app  Application android.app  AA103_POCTheme android.app.Activity  AuthGate android.app.Activity  Bundle android.app.Activity  CreateProjectScreen android.app.Activity  CreateProjectViewModel android.app.Activity  FirebaseAuth android.app.Activity  LaunchedEffect android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  collectAsState android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  finish android.app.Activity  getValue android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  provideDelegate android.app.Activity  
setContent android.app.Activity  	viewModel android.app.Activity  Context android.content  Intent android.content  AA103_POCTheme android.content.Context  AuthGate android.content.Context  Bundle android.content.Context  CreateProjectScreen android.content.Context  CreateProjectViewModel android.content.Context  FirebaseAuth android.content.Context  LaunchedEffect android.content.Context  Modifier android.content.Context  Scaffold android.content.Context  collectAsState android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  finish android.content.Context  getValue android.content.Context  onCreate android.content.Context  padding android.content.Context  provideDelegate android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  	viewModel android.content.Context  AA103_POCTheme android.content.ContextWrapper  AuthGate android.content.ContextWrapper  Bundle android.content.ContextWrapper  CreateProjectScreen android.content.ContextWrapper  CreateProjectViewModel android.content.ContextWrapper  FirebaseAuth android.content.ContextWrapper  LaunchedEffect android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  collectAsState android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  finish android.content.ContextWrapper  getValue android.content.ContextWrapper  onCreate android.content.ContextWrapper  padding android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
setContent android.content.ContextWrapper  	viewModel android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Log android.util  Patterns android.util  d android.util.Log  e android.util.Log  w android.util.Log  
EMAIL_ADDRESS android.util.Patterns  AA103_POCTheme  android.view.ContextThemeWrapper  AuthGate  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  CreateProjectScreen  android.view.ContextThemeWrapper  CreateProjectViewModel  android.view.ContextThemeWrapper  FirebaseAuth  android.view.ContextThemeWrapper  LaunchedEffect  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  collectAsState  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  	viewModel  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  AA103_POCTheme #androidx.activity.ComponentActivity  AuthGate #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  CreateProjectScreen #androidx.activity.ComponentActivity  CreateProjectViewModel #androidx.activity.ComponentActivity  FirebaseAuth #androidx.activity.ComponentActivity  LaunchedEffect #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  collectAsState #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  	viewModel #androidx.activity.ComponentActivity  AA103_POCTheme -androidx.activity.ComponentActivity.Companion  AuthGate -androidx.activity.ComponentActivity.Companion  CreateProjectScreen -androidx.activity.ComponentActivity.Companion  FirebaseAuth -androidx.activity.ComponentActivity.Companion  LaunchedEffect -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  collectAsState -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  finish -androidx.activity.ComponentActivity.Companion  getCOLLECTAsState -androidx.activity.ComponentActivity.Companion  getCollectAsState -androidx.activity.ComponentActivity.Companion  getFILLMaxSize -androidx.activity.ComponentActivity.Companion  getFillMaxSize -androidx.activity.ComponentActivity.Companion  getGETValue -androidx.activity.ComponentActivity.Companion  getGetValue -androidx.activity.ComponentActivity.Companion  
getPADDING -androidx.activity.ComponentActivity.Companion  getPROVIDEDelegate -androidx.activity.ComponentActivity.Companion  
getPadding -androidx.activity.ComponentActivity.Companion  getProvideDelegate -androidx.activity.ComponentActivity.Companion  getVIEWModel -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  getViewModel -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  	viewModel -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  ScrollState androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  AA103_POCTheme "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  CreateProjectScreen "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  JobTypeDropdown "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  ProjectTypeDropdown "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  enableEdgeToEdge "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  finish "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  	viewModel "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  CreateProjectActivity +androidx.compose.foundation.layout.BoxScope  
EmptyProjects +androidx.compose.foundation.layout.BoxScope  Intent +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  ProjectsList +androidx.compose.foundation.layout.BoxScope  ProjectsUiState +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  java +androidx.compose.foundation.layout.BoxScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Intent .androidx.compose.foundation.layout.ColumnScope  JobTypeDropdown .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  ProjectTypeDropdown .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  SignInActivity .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
Visibility .androidx.compose.foundation.layout.ColumnScope  
VisibilityOff .androidx.compose.foundation.layout.ColumnScope  VisualTransformation .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getTRIM .androidx.compose.foundation.layout.ColumnScope  getTrim .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  java .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
getISNotEmpty +androidx.compose.foundation.layout.RowScope  
getIsNotEmpty +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getTRIM +androidx.compose.foundation.layout.RowScope  getTrim +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  trim +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  HorizontalDivider .androidx.compose.foundation.lazy.LazyItemScope  ListItem .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  HorizontalDivider .androidx.compose.foundation.lazy.LazyListScope  ListItem .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  KeyboardOptions  androidx.compose.foundation.text  invoke :androidx.compose.foundation.text.KeyboardOptions.Companion  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  
Visibility ,androidx.compose.material.icons.Icons.Filled  
VisibilityOff ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  AA103_POCTheme androidx.compose.material3  Arrangement androidx.compose.material3  Button androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  CreateProjectScreen androidx.compose.material3  DropdownMenuItem androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  FloatingActionButton androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  JobTypeDropdown androidx.compose.material3  LaunchedEffect androidx.compose.material3  ListItem androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  ProjectTypeDropdown androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Spacer androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  collectAsState androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  enableEdgeToEdge androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  finish androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotEmpty androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  trim androidx.compose.material3  	viewModel androidx.compose.material3  error &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  getFILLMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  getFillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  AA103_POCTheme androidx.compose.runtime  Arrangement androidx.compose.runtime  Button androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  CreateProjectScreen androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  JobTypeDropdown androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  ProjectTypeDropdown androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  collectAsState androidx.compose.runtime  enableEdgeToEdge androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  finish androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  trim androidx.compose.runtime  	viewModel androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  FirebaseAuth .androidx.compose.runtime.DisposableEffectScope  	onDispose .androidx.compose.runtime.DisposableEffectScope  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  getFILLMaxSize androidx.compose.ui.Modifier  getFillMaxSize androidx.compose.ui.Modifier  
getMENUAnchor androidx.compose.ui.Modifier  
getMenuAnchor androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  getVERTICALScroll androidx.compose.ui.Modifier  getVerticalScroll androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getALIGN &androidx.compose.ui.Modifier.Companion  getAlign &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  None 3androidx.compose.ui.text.input.VisualTransformation  None =androidx.compose.ui.text.input.VisualTransformation.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AA103_POCTheme #androidx.core.app.ComponentActivity  AuthGate #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CreateProjectScreen #androidx.core.app.ComponentActivity  CreateProjectViewModel #androidx.core.app.ComponentActivity  FirebaseAuth #androidx.core.app.ComponentActivity  LaunchedEffect #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  collectAsState #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  	viewModel #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	Lifecycle androidx.lifecycle  LifecycleOwner androidx.lifecycle  	ViewModel androidx.lifecycle  repeatOnLifecycle androidx.lifecycle  viewModelScope androidx.lifecycle  State androidx.lifecycle.Lifecycle  getREPEATOnLifecycle androidx.lifecycle.Lifecycle  getRepeatOnLifecycle androidx.lifecycle.Lifecycle  repeatOnLifecycle androidx.lifecycle.Lifecycle  STARTED "androidx.lifecycle.Lifecycle.State  	lifecycle !androidx.lifecycle.LifecycleOwner  Boolean androidx.lifecycle.ViewModel  CreateProjectUiState androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  Firebase androidx.lifecycle.ViewModel  FirebaseAuth androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Job androidx.lifecycle.ViewModel  Log androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  ProjectRepository androidx.lifecycle.ViewModel  ProjectRepositoryInterface androidx.lifecycle.ViewModel  ProjectsUiState androidx.lifecycle.ViewModel  
SignInUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  TAG androidx.lifecycle.ViewModel  _state androidx.lifecycle.ViewModel  _uiState androidx.lifecycle.ViewModel  android androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  auth androidx.lifecycle.ViewModel  catch androidx.lifecycle.ViewModel  
clearError androidx.lifecycle.ViewModel  
createProject androidx.lifecycle.ViewModel  isBlank androidx.lifecycle.ViewModel  isEmpty androidx.lifecycle.ViewModel  isValidEmail androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  	onCleared androidx.lifecycle.ViewModel  projectRepository androidx.lifecycle.ViewModel  
repository androidx.lifecycle.ViewModel  signIn androidx.lifecycle.ViewModel  startStreaming androidx.lifecycle.ViewModel  
stopStreaming androidx.lifecycle.ViewModel  togglePasswordVisibility androidx.lifecycle.ViewModel  trim androidx.lifecycle.ViewModel  updateEmail androidx.lifecycle.ViewModel  
updateJobType androidx.lifecycle.ViewModel  updatePassword androidx.lifecycle.ViewModel  updateTitle androidx.lifecycle.ViewModel  
updateType androidx.lifecycle.ViewModel  validateInputs androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  	viewModel $androidx.lifecycle.viewmodel.compose  AA103Application com.dev.aa103_poc  AA103_POCTheme com.dev.aa103_poc  AuthGate com.dev.aa103_poc  Button com.dev.aa103_poc  FirebaseAuth com.dev.aa103_poc  Intent com.dev.aa103_poc  MainActivity com.dev.aa103_poc  
MainScreen com.dev.aa103_poc  MainScreenPreview com.dev.aa103_poc  Modifier com.dev.aa103_poc  Scaffold com.dev.aa103_poc  SignInActivity com.dev.aa103_poc  Text com.dev.aa103_poc  enableEdgeToEdge com.dev.aa103_poc  fillMaxSize com.dev.aa103_poc  java com.dev.aa103_poc  padding com.dev.aa103_poc  
setContent com.dev.aa103_poc  AA103_POCTheme com.dev.aa103_poc.MainActivity  AuthGate com.dev.aa103_poc.MainActivity  Bundle com.dev.aa103_poc.MainActivity  FirebaseAuth com.dev.aa103_poc.MainActivity  Modifier com.dev.aa103_poc.MainActivity  Scaffold com.dev.aa103_poc.MainActivity  enableEdgeToEdge com.dev.aa103_poc.MainActivity  fillMaxSize com.dev.aa103_poc.MainActivity  getENABLEEdgeToEdge com.dev.aa103_poc.MainActivity  getEnableEdgeToEdge com.dev.aa103_poc.MainActivity  getFILLMaxSize com.dev.aa103_poc.MainActivity  getFillMaxSize com.dev.aa103_poc.MainActivity  
getSETContent com.dev.aa103_poc.MainActivity  
getSetContent com.dev.aa103_poc.MainActivity  
setContent com.dev.aa103_poc.MainActivity  Boolean com.dev.aa103_poc.data.model  List com.dev.aa103_poc.data.model  Project com.dev.aa103_poc.data.model  
ProjectStream com.dev.aa103_poc.data.model  String com.dev.aa103_poc.data.model  listOf com.dev.aa103_poc.data.model  	Companion $com.dev.aa103_poc.data.model.Project  JOB_TYPE_PAINTING $com.dev.aa103_poc.data.model.Project  List $com.dev.aa103_poc.data.model.Project  String $com.dev.aa103_poc.data.model.Project  	TYPE_WALL $com.dev.aa103_poc.data.model.Project  	Timestamp $com.dev.aa103_poc.data.model.Project  getAvailableJobTypes $com.dev.aa103_poc.data.model.Project  getAvailableTypes $com.dev.aa103_poc.data.model.Project  id $com.dev.aa103_poc.data.model.Project  jobType $com.dev.aa103_poc.data.model.Project  listOf $com.dev.aa103_poc.data.model.Project  title $com.dev.aa103_poc.data.model.Project  type $com.dev.aa103_poc.data.model.Project  JOB_TYPE_PAINTING .com.dev.aa103_poc.data.model.Project.Companion  List .com.dev.aa103_poc.data.model.Project.Companion  String .com.dev.aa103_poc.data.model.Project.Companion  	TYPE_WALL .com.dev.aa103_poc.data.model.Project.Companion  	Timestamp .com.dev.aa103_poc.data.model.Project.Companion  getAvailableJobTypes .com.dev.aa103_poc.data.model.Project.Companion  getAvailableTypes .com.dev.aa103_poc.data.model.Project.Companion  	getLISTOf .com.dev.aa103_poc.data.model.Project.Companion  	getListOf .com.dev.aa103_poc.data.model.Project.Companion  invoke .com.dev.aa103_poc.data.model.Project.Companion  listOf .com.dev.aa103_poc.data.model.Project.Companion  Boolean *com.dev.aa103_poc.data.model.ProjectStream  List *com.dev.aa103_poc.data.model.ProjectStream  Project *com.dev.aa103_poc.data.model.ProjectStream  	fromCache *com.dev.aa103_poc.data.model.ProjectStream  hasPendingWrites *com.dev.aa103_poc.data.model.ProjectStream  items *com.dev.aa103_poc.data.model.ProjectStream  	Exception !com.dev.aa103_poc.data.repository  
FieldValue !com.dev.aa103_poc.data.repository  FirebaseAuth !com.dev.aa103_poc.data.repository  FirebaseFirestore !com.dev.aa103_poc.data.repository  FirestoreProjectRepository !com.dev.aa103_poc.data.repository  IllegalStateException !com.dev.aa103_poc.data.repository  List !com.dev.aa103_poc.data.repository  Project !com.dev.aa103_poc.data.repository  ProjectRepository !com.dev.aa103_poc.data.repository  ProjectRepositoryInterface !com.dev.aa103_poc.data.repository  
ProjectStream !com.dev.aa103_poc.data.repository  Query !com.dev.aa103_poc.data.repository  Source !com.dev.aa103_poc.data.repository  String !com.dev.aa103_poc.data.repository  await !com.dev.aa103_poc.data.repository  
awaitClose !com.dev.aa103_poc.data.repository  callbackFlow !com.dev.aa103_poc.data.repository  com !com.dev.aa103_poc.data.repository  db !com.dev.aa103_poc.data.repository  java !com.dev.aa103_poc.data.repository  let !com.dev.aa103_poc.data.repository  map !com.dev.aa103_poc.data.repository  mapDocumentToProject !com.dev.aa103_poc.data.repository  
mapNotNull !com.dev.aa103_poc.data.repository  mapOf !com.dev.aa103_poc.data.repository  to !com.dev.aa103_poc.data.repository  trim !com.dev.aa103_poc.data.repository  	Exception <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  FirebaseFirestore <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  Flow <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  Inject <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  Project <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  
ProjectStream <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  Query <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  Source <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  String <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  await <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  
awaitClose <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  callbackFlow <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  com <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  db <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  getAWAIT <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  getAwait <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  getCALLBACKFlow <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  getCallbackFlow <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  getMAP <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  getMap <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  invoke <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  map <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  mapDocumentToProject <com.dev.aa103_poc.data.repository.FirestoreProjectRepository  
FieldValue 3com.dev.aa103_poc.data.repository.ProjectRepository  FirebaseAuth 3com.dev.aa103_poc.data.repository.ProjectRepository  FirebaseFirestore 3com.dev.aa103_poc.data.repository.ProjectRepository  IllegalStateException 3com.dev.aa103_poc.data.repository.ProjectRepository  List 3com.dev.aa103_poc.data.repository.ProjectRepository  Project 3com.dev.aa103_poc.data.repository.ProjectRepository  String 3com.dev.aa103_poc.data.repository.ProjectRepository  auth 3com.dev.aa103_poc.data.repository.ProjectRepository  await 3com.dev.aa103_poc.data.repository.ProjectRepository  
createProject 3com.dev.aa103_poc.data.repository.ProjectRepository  	firestore 3com.dev.aa103_poc.data.repository.ProjectRepository  getAWAIT 3com.dev.aa103_poc.data.repository.ProjectRepository  getAwait 3com.dev.aa103_poc.data.repository.ProjectRepository  getLET 3com.dev.aa103_poc.data.repository.ProjectRepository  getLet 3com.dev.aa103_poc.data.repository.ProjectRepository  
getMAPNotNull 3com.dev.aa103_poc.data.repository.ProjectRepository  getMAPOf 3com.dev.aa103_poc.data.repository.ProjectRepository  
getMapNotNull 3com.dev.aa103_poc.data.repository.ProjectRepository  getMapOf 3com.dev.aa103_poc.data.repository.ProjectRepository  getProjectsCollection 3com.dev.aa103_poc.data.repository.ProjectRepository  getTO 3com.dev.aa103_poc.data.repository.ProjectRepository  getTRIM 3com.dev.aa103_poc.data.repository.ProjectRepository  getTo 3com.dev.aa103_poc.data.repository.ProjectRepository  getTrim 3com.dev.aa103_poc.data.repository.ProjectRepository  java 3com.dev.aa103_poc.data.repository.ProjectRepository  let 3com.dev.aa103_poc.data.repository.ProjectRepository  
mapNotNull 3com.dev.aa103_poc.data.repository.ProjectRepository  mapOf 3com.dev.aa103_poc.data.repository.ProjectRepository  to 3com.dev.aa103_poc.data.repository.ProjectRepository  trim 3com.dev.aa103_poc.data.repository.ProjectRepository  Flow <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  
ProjectStream <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  String <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  stream <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  	AppModule com.dev.aa103_poc.di  FirebaseAuth com.dev.aa103_poc.di  FirebaseFirestore com.dev.aa103_poc.di  RepositoryModule com.dev.aa103_poc.di  SingletonComponent com.dev.aa103_poc.di  FirebaseAuth com.dev.aa103_poc.di.AppModule  FirebaseFirestore com.dev.aa103_poc.di.AppModule  Provides com.dev.aa103_poc.di.AppModule  	Singleton com.dev.aa103_poc.di.AppModule  Binds %com.dev.aa103_poc.di.RepositoryModule  FirestoreProjectRepository %com.dev.aa103_poc.di.RepositoryModule  ProjectRepositoryInterface %com.dev.aa103_poc.di.RepositoryModule  	Singleton %com.dev.aa103_poc.di.RepositoryModule  AuthGate com.dev.aa103_poc.ui.auth  FirebaseAuth com.dev.aa103_poc.ui.auth  Unit com.dev.aa103_poc.ui.auth  provideDelegate com.dev.aa103_poc.ui.auth  AA103_POCTheme "com.dev.aa103_poc.ui.createproject  Arrangement "com.dev.aa103_poc.ui.createproject  Boolean "com.dev.aa103_poc.ui.createproject  Button "com.dev.aa103_poc.ui.createproject  CircularProgressIndicator "com.dev.aa103_poc.ui.createproject  Column "com.dev.aa103_poc.ui.createproject  
Composable "com.dev.aa103_poc.ui.createproject  CreateProjectActivity "com.dev.aa103_poc.ui.createproject  CreateProjectScreen "com.dev.aa103_poc.ui.createproject  CreateProjectScreenPreview "com.dev.aa103_poc.ui.createproject  CreateProjectUiState "com.dev.aa103_poc.ui.createproject  CreateProjectViewModel "com.dev.aa103_poc.ui.createproject  DropdownMenuItem "com.dev.aa103_poc.ui.createproject  	Exception "com.dev.aa103_poc.ui.createproject  ExperimentalMaterial3Api "com.dev.aa103_poc.ui.createproject  ExposedDropdownMenuBox "com.dev.aa103_poc.ui.createproject  ExposedDropdownMenuDefaults "com.dev.aa103_poc.ui.createproject  JobTypeDropdown "com.dev.aa103_poc.ui.createproject  LaunchedEffect "com.dev.aa103_poc.ui.createproject  Log "com.dev.aa103_poc.ui.createproject  
MaterialTheme "com.dev.aa103_poc.ui.createproject  Modifier "com.dev.aa103_poc.ui.createproject  MutableStateFlow "com.dev.aa103_poc.ui.createproject  OptIn "com.dev.aa103_poc.ui.createproject  OutlinedButton "com.dev.aa103_poc.ui.createproject  OutlinedTextField "com.dev.aa103_poc.ui.createproject  ProjectRepository "com.dev.aa103_poc.ui.createproject  ProjectTypeDropdown "com.dev.aa103_poc.ui.createproject  Row "com.dev.aa103_poc.ui.createproject  Scaffold "com.dev.aa103_poc.ui.createproject  Spacer "com.dev.aa103_poc.ui.createproject  String "com.dev.aa103_poc.ui.createproject  TAG "com.dev.aa103_poc.ui.createproject  Text "com.dev.aa103_poc.ui.createproject  	TextAlign "com.dev.aa103_poc.ui.createproject  Unit "com.dev.aa103_poc.ui.createproject  _uiState "com.dev.aa103_poc.ui.createproject  asStateFlow "com.dev.aa103_poc.ui.createproject  collectAsState "com.dev.aa103_poc.ui.createproject  enableEdgeToEdge "com.dev.aa103_poc.ui.createproject  fillMaxSize "com.dev.aa103_poc.ui.createproject  fillMaxWidth "com.dev.aa103_poc.ui.createproject  finish "com.dev.aa103_poc.ui.createproject  forEach "com.dev.aa103_poc.ui.createproject  getValue "com.dev.aa103_poc.ui.createproject  height "com.dev.aa103_poc.ui.createproject  isEmpty "com.dev.aa103_poc.ui.createproject  
isNotEmpty "com.dev.aa103_poc.ui.createproject  launch "com.dev.aa103_poc.ui.createproject  mutableStateOf "com.dev.aa103_poc.ui.createproject  padding "com.dev.aa103_poc.ui.createproject  projectRepository "com.dev.aa103_poc.ui.createproject  provideDelegate "com.dev.aa103_poc.ui.createproject  remember "com.dev.aa103_poc.ui.createproject  
setContent "com.dev.aa103_poc.ui.createproject  setValue "com.dev.aa103_poc.ui.createproject  size "com.dev.aa103_poc.ui.createproject  trim "com.dev.aa103_poc.ui.createproject  	viewModel "com.dev.aa103_poc.ui.createproject  viewModelScope "com.dev.aa103_poc.ui.createproject  AA103_POCTheme 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  Bundle 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  CreateProjectScreen 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  CreateProjectViewModel 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  LaunchedEffect 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  Modifier 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  Scaffold 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  collectAsState 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  enableEdgeToEdge 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  fillMaxSize 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  finish 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getCOLLECTAsState 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getCollectAsState 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getENABLEEdgeToEdge 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getEnableEdgeToEdge 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getFILLMaxSize 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getFillMaxSize 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getGETValue 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getGetValue 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  
getPADDING 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getPROVIDEDelegate 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  
getPadding 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getProvideDelegate 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  
getSETContent 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  
getSetContent 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getVIEWModel 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getValue 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  getViewModel 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  padding 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  provideDelegate 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  
setContent 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  	viewModel 8com.dev.aa103_poc.ui.createproject.CreateProjectActivity  Boolean 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  Project 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  String 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  copy 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  errorMessage 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  	isLoading 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  isProjectCreated 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  selectedJobType 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  selectedType 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  title 7com.dev.aa103_poc.ui.createproject.CreateProjectUiState  Boolean 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  CreateProjectUiState 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  	Exception 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  Log 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  MutableStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  ProjectRepository 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  	StateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  String 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  TAG 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  _uiState 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  asStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  
clearError 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  
createProject 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getASStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getAsStateFlow 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  
getISEmpty 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  
getIsEmpty 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  	getLAUNCH 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  	getLaunch 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getTRIM 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getTrim 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getVIEWModelScope 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  getViewModelScope 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  isEmpty 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  launch 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  projectRepository 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  trim 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  uiState 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  
updateJobType 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  updateTitle 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  
updateType 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  validateInputs 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  viewModelScope 9com.dev.aa103_poc.ui.createproject.CreateProjectViewModel  Boolean Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  CreateProjectUiState Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  	Exception Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  Log Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  MutableStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  ProjectRepository Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  	StateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  String Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  TAG Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  _uiState Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  asStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  getASStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  getAsStateFlow Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  
getISEmpty Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  
getIsEmpty Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  	getLAUNCH Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  	getLaunch Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  getTRIM Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  getTrim Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  isEmpty Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  launch Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  projectRepository Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  trim Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  viewModelScope Ccom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion  	Alignment com.dev.aa103_poc.ui.projects  Boolean com.dev.aa103_poc.ui.projects  Button com.dev.aa103_poc.ui.projects  CircularProgressIndicator com.dev.aa103_poc.ui.projects  CreateProjectActivity com.dev.aa103_poc.ui.projects  
EmptyProjects com.dev.aa103_poc.ui.projects  ExperimentalMaterial3Api com.dev.aa103_poc.ui.projects  HorizontalDivider com.dev.aa103_poc.ui.projects  Intent com.dev.aa103_poc.ui.projects  	Lifecycle com.dev.aa103_poc.ui.projects  List com.dev.aa103_poc.ui.projects  ListItem com.dev.aa103_poc.ui.projects  
MaterialTheme com.dev.aa103_poc.ui.projects  Modifier com.dev.aa103_poc.ui.projects  MutableStateFlow com.dev.aa103_poc.ui.projects  OptIn com.dev.aa103_poc.ui.projects  ProjectsList com.dev.aa103_poc.ui.projects  
ProjectsRoute com.dev.aa103_poc.ui.projects  ProjectsScreen com.dev.aa103_poc.ui.projects  ProjectsScreenPreview com.dev.aa103_poc.ui.projects  ProjectsUiState com.dev.aa103_poc.ui.projects  ProjectsViewModel com.dev.aa103_poc.ui.projects  Spacer com.dev.aa103_poc.ui.projects  String com.dev.aa103_poc.ui.projects  Text com.dev.aa103_poc.ui.projects  Unit com.dev.aa103_poc.ui.projects  _state com.dev.aa103_poc.ui.projects  catch com.dev.aa103_poc.ui.projects  height com.dev.aa103_poc.ui.projects  items com.dev.aa103_poc.ui.projects  java com.dev.aa103_poc.ui.projects  launch com.dev.aa103_poc.ui.projects  provideDelegate com.dev.aa103_poc.ui.projects  repeatOnLifecycle com.dev.aa103_poc.ui.projects  
repository com.dev.aa103_poc.ui.projects  viewModelScope com.dev.aa103_poc.ui.projects  Boolean -com.dev.aa103_poc.ui.projects.ProjectsUiState  Content -com.dev.aa103_poc.ui.projects.ProjectsUiState  Empty -com.dev.aa103_poc.ui.projects.ProjectsUiState  Error -com.dev.aa103_poc.ui.projects.ProjectsUiState  List -com.dev.aa103_poc.ui.projects.ProjectsUiState  Loading -com.dev.aa103_poc.ui.projects.ProjectsUiState  Project -com.dev.aa103_poc.ui.projects.ProjectsUiState  ProjectsUiState -com.dev.aa103_poc.ui.projects.ProjectsUiState  String -com.dev.aa103_poc.ui.projects.ProjectsUiState  message -com.dev.aa103_poc.ui.projects.ProjectsUiState  projects -com.dev.aa103_poc.ui.projects.ProjectsUiState  Boolean 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  List 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  Project 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  	fromCache 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  projects 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  syncing 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  String 3com.dev.aa103_poc.ui.projects.ProjectsUiState.Error  message 3com.dev.aa103_poc.ui.projects.ProjectsUiState.Error  FirebaseAuth /com.dev.aa103_poc.ui.projects.ProjectsViewModel  Inject /com.dev.aa103_poc.ui.projects.ProjectsViewModel  Job /com.dev.aa103_poc.ui.projects.ProjectsViewModel  MutableStateFlow /com.dev.aa103_poc.ui.projects.ProjectsViewModel  ProjectRepositoryInterface /com.dev.aa103_poc.ui.projects.ProjectsViewModel  ProjectsUiState /com.dev.aa103_poc.ui.projects.ProjectsViewModel  	StateFlow /com.dev.aa103_poc.ui.projects.ProjectsViewModel  _state /com.dev.aa103_poc.ui.projects.ProjectsViewModel  auth /com.dev.aa103_poc.ui.projects.ProjectsViewModel  catch /com.dev.aa103_poc.ui.projects.ProjectsViewModel  getCATCH /com.dev.aa103_poc.ui.projects.ProjectsViewModel  getCatch /com.dev.aa103_poc.ui.projects.ProjectsViewModel  	getLAUNCH /com.dev.aa103_poc.ui.projects.ProjectsViewModel  	getLaunch /com.dev.aa103_poc.ui.projects.ProjectsViewModel  getVIEWModelScope /com.dev.aa103_poc.ui.projects.ProjectsViewModel  getViewModelScope /com.dev.aa103_poc.ui.projects.ProjectsViewModel  launch /com.dev.aa103_poc.ui.projects.ProjectsViewModel  
repository /com.dev.aa103_poc.ui.projects.ProjectsViewModel  startStreaming /com.dev.aa103_poc.ui.projects.ProjectsViewModel  state /com.dev.aa103_poc.ui.projects.ProjectsViewModel  
stopStreaming /com.dev.aa103_poc.ui.projects.ProjectsViewModel  streamingJob /com.dev.aa103_poc.ui.projects.ProjectsViewModel  viewModelScope /com.dev.aa103_poc.ui.projects.ProjectsViewModel  AA103_POCTheme com.dev.aa103_poc.ui.signin  AuthGate com.dev.aa103_poc.ui.signin  Boolean com.dev.aa103_poc.ui.signin  Button com.dev.aa103_poc.ui.signin  CircularProgressIndicator com.dev.aa103_poc.ui.signin  Firebase com.dev.aa103_poc.ui.signin  FirebaseAuth com.dev.aa103_poc.ui.signin  Icon com.dev.aa103_poc.ui.signin  
IconButton com.dev.aa103_poc.ui.signin  Icons com.dev.aa103_poc.ui.signin  KeyboardOptions com.dev.aa103_poc.ui.signin  KeyboardType com.dev.aa103_poc.ui.signin  Log com.dev.aa103_poc.ui.signin  
MaterialTheme com.dev.aa103_poc.ui.signin  Modifier com.dev.aa103_poc.ui.signin  MutableStateFlow com.dev.aa103_poc.ui.signin  OutlinedTextField com.dev.aa103_poc.ui.signin  PasswordVisualTransformation com.dev.aa103_poc.ui.signin  Scaffold com.dev.aa103_poc.ui.signin  SignInActivity com.dev.aa103_poc.ui.signin  
SignInContent com.dev.aa103_poc.ui.signin  SignInScreen com.dev.aa103_poc.ui.signin  SignInScreenPreview com.dev.aa103_poc.ui.signin  
SignInUiState com.dev.aa103_poc.ui.signin  SignInViewModel com.dev.aa103_poc.ui.signin  Spacer com.dev.aa103_poc.ui.signin  String com.dev.aa103_poc.ui.signin  TAG com.dev.aa103_poc.ui.signin  Text com.dev.aa103_poc.ui.signin  Unit com.dev.aa103_poc.ui.signin  VisualTransformation com.dev.aa103_poc.ui.signin  android com.dev.aa103_poc.ui.signin  asStateFlow com.dev.aa103_poc.ui.signin  enableEdgeToEdge com.dev.aa103_poc.ui.signin  fillMaxSize com.dev.aa103_poc.ui.signin  fillMaxWidth com.dev.aa103_poc.ui.signin  height com.dev.aa103_poc.ui.signin  isBlank com.dev.aa103_poc.ui.signin  padding com.dev.aa103_poc.ui.signin  provideDelegate com.dev.aa103_poc.ui.signin  
setContent com.dev.aa103_poc.ui.signin  AA103_POCTheme *com.dev.aa103_poc.ui.signin.SignInActivity  AuthGate *com.dev.aa103_poc.ui.signin.SignInActivity  Bundle *com.dev.aa103_poc.ui.signin.SignInActivity  FirebaseAuth *com.dev.aa103_poc.ui.signin.SignInActivity  Modifier *com.dev.aa103_poc.ui.signin.SignInActivity  Scaffold *com.dev.aa103_poc.ui.signin.SignInActivity  enableEdgeToEdge *com.dev.aa103_poc.ui.signin.SignInActivity  fillMaxSize *com.dev.aa103_poc.ui.signin.SignInActivity  getENABLEEdgeToEdge *com.dev.aa103_poc.ui.signin.SignInActivity  getEnableEdgeToEdge *com.dev.aa103_poc.ui.signin.SignInActivity  getFILLMaxSize *com.dev.aa103_poc.ui.signin.SignInActivity  getFillMaxSize *com.dev.aa103_poc.ui.signin.SignInActivity  
getSETContent *com.dev.aa103_poc.ui.signin.SignInActivity  
getSetContent *com.dev.aa103_poc.ui.signin.SignInActivity  
setContent *com.dev.aa103_poc.ui.signin.SignInActivity  Boolean )com.dev.aa103_poc.ui.signin.SignInUiState  String )com.dev.aa103_poc.ui.signin.SignInUiState  copy )com.dev.aa103_poc.ui.signin.SignInUiState  email )com.dev.aa103_poc.ui.signin.SignInUiState  errorMessage )com.dev.aa103_poc.ui.signin.SignInUiState  	isLoading )com.dev.aa103_poc.ui.signin.SignInUiState  isPasswordVisible )com.dev.aa103_poc.ui.signin.SignInUiState  password )com.dev.aa103_poc.ui.signin.SignInUiState  Boolean +com.dev.aa103_poc.ui.signin.SignInViewModel  Firebase +com.dev.aa103_poc.ui.signin.SignInViewModel  FirebaseAuth +com.dev.aa103_poc.ui.signin.SignInViewModel  Log +com.dev.aa103_poc.ui.signin.SignInViewModel  MutableStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
SignInUiState +com.dev.aa103_poc.ui.signin.SignInViewModel  	StateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  String +com.dev.aa103_poc.ui.signin.SignInViewModel  TAG +com.dev.aa103_poc.ui.signin.SignInViewModel  _uiState +com.dev.aa103_poc.ui.signin.SignInViewModel  android +com.dev.aa103_poc.ui.signin.SignInViewModel  asStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  auth +com.dev.aa103_poc.ui.signin.SignInViewModel  
clearError +com.dev.aa103_poc.ui.signin.SignInViewModel  
getANDROID +com.dev.aa103_poc.ui.signin.SignInViewModel  getASStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
getAndroid +com.dev.aa103_poc.ui.signin.SignInViewModel  getAsStateFlow +com.dev.aa103_poc.ui.signin.SignInViewModel  
getISBlank +com.dev.aa103_poc.ui.signin.SignInViewModel  
getIsBlank +com.dev.aa103_poc.ui.signin.SignInViewModel  isBlank +com.dev.aa103_poc.ui.signin.SignInViewModel  isValidEmail +com.dev.aa103_poc.ui.signin.SignInViewModel  signIn +com.dev.aa103_poc.ui.signin.SignInViewModel  togglePasswordVisibility +com.dev.aa103_poc.ui.signin.SignInViewModel  uiState +com.dev.aa103_poc.ui.signin.SignInViewModel  updateEmail +com.dev.aa103_poc.ui.signin.SignInViewModel  updatePassword +com.dev.aa103_poc.ui.signin.SignInViewModel  Boolean 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  Firebase 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  FirebaseAuth 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  Log 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  MutableStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  
SignInUiState 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  	StateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  String 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  TAG 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  android 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  asStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  auth 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  
getANDROID 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  getASStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  
getAndroid 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  getAsStateFlow 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  
getISBlank 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  
getIsBlank 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  isBlank 5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion  AA103_POCTheme com.dev.aa103_poc.ui.theme  Boolean com.dev.aa103_poc.ui.theme  Build com.dev.aa103_poc.ui.theme  DarkColorScheme com.dev.aa103_poc.ui.theme  LightColorScheme com.dev.aa103_poc.ui.theme  Pink40 com.dev.aa103_poc.ui.theme  Pink80 com.dev.aa103_poc.ui.theme  Purple40 com.dev.aa103_poc.ui.theme  Purple80 com.dev.aa103_poc.ui.theme  PurpleGrey40 com.dev.aa103_poc.ui.theme  PurpleGrey80 com.dev.aa103_poc.ui.theme  
Typography com.dev.aa103_poc.ui.theme  Unit com.dev.aa103_poc.ui.theme  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  addOnCompleteListener !com.google.android.gms.tasks.Task  await !com.google.android.gms.tasks.Task  	exception !com.google.android.gms.tasks.Task  getAWAIT !com.google.android.gms.tasks.Task  getAwait !com.google.android.gms.tasks.Task  getEXCEPTION !com.google.android.gms.tasks.Task  getException !com.google.android.gms.tasks.Task  getISSuccessful !com.google.android.gms.tasks.Task  getIsSuccessful !com.google.android.gms.tasks.Task  isSuccessful !com.google.android.gms.tasks.Task  setException !com.google.android.gms.tasks.Task  
setSuccessful !com.google.android.gms.tasks.Task  	Timestamp com.google.firebase  
AuthResult com.google.firebase.auth  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  AuthStateListener %com.google.firebase.auth.FirebaseAuth  addAuthStateListener %com.google.firebase.auth.FirebaseAuth  currentUser %com.google.firebase.auth.FirebaseAuth  getCURRENTUser %com.google.firebase.auth.FirebaseAuth  getCurrentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  removeAuthStateListener %com.google.firebase.auth.FirebaseAuth  setCurrentUser %com.google.firebase.auth.FirebaseAuth  signInWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  <SAM-CONSTRUCTOR> 7com.google.firebase.auth.FirebaseAuth.AuthStateListener  equals %com.google.firebase.auth.FirebaseUser  getUID %com.google.firebase.auth.FirebaseUser  getUid %com.google.firebase.auth.FirebaseUser  setUid %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  auth com.google.firebase.auth.ktx  CollectionReference com.google.firebase.firestore  DocumentReference com.google.firebase.firestore  DocumentSnapshot com.google.firebase.firestore  
FieldValue com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  FirebaseFirestoreException com.google.firebase.firestore  ListenerRegistration com.google.firebase.firestore  Query com.google.firebase.firestore  
QuerySnapshot com.google.firebase.firestore  Source com.google.firebase.firestore  document 1com.google.firebase.firestore.CollectionReference  get 1com.google.firebase.firestore.CollectionReference  orderBy 1com.google.firebase.firestore.CollectionReference  
collection /com.google.firebase.firestore.DocumentReference  getID /com.google.firebase.firestore.DocumentReference  getId /com.google.firebase.firestore.DocumentReference  id /com.google.firebase.firestore.DocumentReference  set /com.google.firebase.firestore.DocumentReference  setId /com.google.firebase.firestore.DocumentReference  getID .com.google.firebase.firestore.DocumentSnapshot  getId .com.google.firebase.firestore.DocumentSnapshot  	getString .com.google.firebase.firestore.DocumentSnapshot  getTimestamp .com.google.firebase.firestore.DocumentSnapshot  id .com.google.firebase.firestore.DocumentSnapshot  setId .com.google.firebase.firestore.DocumentSnapshot  toObject .com.google.firebase.firestore.DocumentSnapshot  <SAM-CONSTRUCTOR> +com.google.firebase.firestore.EventListener  serverTimestamp (com.google.firebase.firestore.FieldValue  
collection /com.google.firebase.firestore.FirebaseFirestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  equals 8com.google.firebase.firestore.FirebaseFirestoreException  remove 2com.google.firebase.firestore.ListenerRegistration  	Direction #com.google.firebase.firestore.Query  addSnapshotListener #com.google.firebase.firestore.Query  document #com.google.firebase.firestore.Query  get #com.google.firebase.firestore.Query  orderBy #com.google.firebase.firestore.Query  
DESCENDING -com.google.firebase.firestore.Query.Direction  	documents +com.google.firebase.firestore.QuerySnapshot  equals +com.google.firebase.firestore.QuerySnapshot  getDOCUMENTS +com.google.firebase.firestore.QuerySnapshot  getDocuments +com.google.firebase.firestore.QuerySnapshot  getMETADATA +com.google.firebase.firestore.QuerySnapshot  getMetadata +com.google.firebase.firestore.QuerySnapshot  metadata +com.google.firebase.firestore.QuerySnapshot  setDocuments +com.google.firebase.firestore.QuerySnapshot  setMetadata +com.google.firebase.firestore.QuerySnapshot  getISFromCache .com.google.firebase.firestore.SnapshotMetadata  getIsFromCache .com.google.firebase.firestore.SnapshotMetadata  hasPendingWrites .com.google.firebase.firestore.SnapshotMetadata  isFromCache .com.google.firebase.firestore.SnapshotMetadata  setFromCache .com.google.firebase.firestore.SnapshotMetadata  CACHE $com.google.firebase.firestore.Source  Firebase com.google.firebase.ktx  auth  com.google.firebase.ktx.Firebase  getAUTH  com.google.firebase.ktx.Firebase  getAuth  com.google.firebase.ktx.Firebase  Binds dagger  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  SingletonComponent dagger.hilt.components  AA103_POCTheme 	java.lang  	Alignment 	java.lang  Arrangement 	java.lang  AuthGate 	java.lang  Build 	java.lang  Button 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  CreateProjectActivity 	java.lang  CreateProjectScreen 	java.lang  CreateProjectUiState 	java.lang  DropdownMenuItem 	java.lang  
EmptyProjects 	java.lang  	Exception 	java.lang  ExperimentalMaterial3Api 	java.lang  ExposedDropdownMenuDefaults 	java.lang  
FieldValue 	java.lang  Firebase 	java.lang  FirebaseAuth 	java.lang  FirebaseFirestore 	java.lang  HorizontalDivider 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  IllegalStateException 	java.lang  Intent 	java.lang  JobTypeDropdown 	java.lang  KeyboardOptions 	java.lang  KeyboardType 	java.lang  LaunchedEffect 	java.lang  	Lifecycle 	java.lang  ListItem 	java.lang  Log 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  MutableStateFlow 	java.lang  OutlinedButton 	java.lang  OutlinedTextField 	java.lang  PasswordVisualTransformation 	java.lang  Project 	java.lang  ProjectRepository 	java.lang  
ProjectStream 	java.lang  ProjectTypeDropdown 	java.lang  ProjectsList 	java.lang  ProjectsUiState 	java.lang  Query 	java.lang  Row 	java.lang  Scaffold 	java.lang  SignInActivity 	java.lang  
SignInUiState 	java.lang  SingletonComponent 	java.lang  Source 	java.lang  Spacer 	java.lang  TAG 	java.lang  Text 	java.lang  	TextAlign 	java.lang  VisualTransformation 	java.lang  Void 	java.lang  _state 	java.lang  _uiState 	java.lang  android 	java.lang  asStateFlow 	java.lang  await 	java.lang  callbackFlow 	java.lang  catch 	java.lang  collectAsState 	java.lang  com 	java.lang  db 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  finish 	java.lang  forEach 	java.lang  getValue 	java.lang  height 	java.lang  isBlank 	java.lang  isEmpty 	java.lang  
isNotEmpty 	java.lang  java 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  map 	java.lang  mapDocumentToProject 	java.lang  
mapNotNull 	java.lang  mapOf 	java.lang  padding 	java.lang  projectRepository 	java.lang  provideDelegate 	java.lang  repeatOnLifecycle 	java.lang  
repository 	java.lang  size 	java.lang  to 	java.lang  trim 	java.lang  	viewModel 	java.lang  message java.lang.Exception  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  Inject javax.inject  	Singleton javax.inject  AA103_POCTheme kotlin  	Alignment kotlin  Any kotlin  Arrangement kotlin  AuthGate kotlin  Boolean kotlin  Build kotlin  Button kotlin  CircularProgressIndicator kotlin  CreateProjectActivity kotlin  CreateProjectScreen kotlin  CreateProjectUiState kotlin  Double kotlin  DropdownMenuItem kotlin  
EmptyProjects kotlin  	Exception kotlin  ExperimentalMaterial3Api kotlin  ExposedDropdownMenuDefaults kotlin  
FieldValue kotlin  Firebase kotlin  FirebaseAuth kotlin  FirebaseFirestore kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  HorizontalDivider kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  IllegalStateException kotlin  Int kotlin  Intent kotlin  JobTypeDropdown kotlin  KeyboardOptions kotlin  KeyboardType kotlin  LaunchedEffect kotlin  	Lifecycle kotlin  ListItem kotlin  Log kotlin  
MaterialTheme kotlin  Modifier kotlin  MutableStateFlow kotlin  Nothing kotlin  OptIn kotlin  OutlinedButton kotlin  OutlinedTextField kotlin  Pair kotlin  PasswordVisualTransformation kotlin  Project kotlin  ProjectRepository kotlin  
ProjectStream kotlin  ProjectTypeDropdown kotlin  ProjectsList kotlin  ProjectsUiState kotlin  Query kotlin  Row kotlin  Scaffold kotlin  SignInActivity kotlin  
SignInUiState kotlin  SingletonComponent kotlin  Source kotlin  Spacer kotlin  String kotlin  TAG kotlin  Text kotlin  	TextAlign kotlin  	Throwable kotlin  Unit kotlin  VisualTransformation kotlin  _state kotlin  _uiState kotlin  android kotlin  asStateFlow kotlin  await kotlin  callbackFlow kotlin  catch kotlin  collectAsState kotlin  com kotlin  db kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  finish kotlin  forEach kotlin  getValue kotlin  height kotlin  isBlank kotlin  isEmpty kotlin  
isNotEmpty kotlin  java kotlin  launch kotlin  let kotlin  listOf kotlin  map kotlin  mapDocumentToProject kotlin  
mapNotNull kotlin  mapOf kotlin  padding kotlin  projectRepository kotlin  provideDelegate kotlin  repeatOnLifecycle kotlin  
repository kotlin  size kotlin  to kotlin  trim kotlin  	viewModel kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  
getISBlank 
kotlin.String  
getISEmpty 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsBlank 
kotlin.String  
getIsEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  getTO 
kotlin.String  getTRIM 
kotlin.String  getTo 
kotlin.String  getTrim 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  AA103_POCTheme kotlin.annotation  	Alignment kotlin.annotation  Arrangement kotlin.annotation  AuthGate kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  CircularProgressIndicator kotlin.annotation  CreateProjectActivity kotlin.annotation  CreateProjectScreen kotlin.annotation  CreateProjectUiState kotlin.annotation  DropdownMenuItem kotlin.annotation  
EmptyProjects kotlin.annotation  	Exception kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  ExposedDropdownMenuDefaults kotlin.annotation  
FieldValue kotlin.annotation  Firebase kotlin.annotation  FirebaseAuth kotlin.annotation  FirebaseFirestore kotlin.annotation  HorizontalDivider kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  IllegalStateException kotlin.annotation  Intent kotlin.annotation  JobTypeDropdown kotlin.annotation  KeyboardOptions kotlin.annotation  KeyboardType kotlin.annotation  LaunchedEffect kotlin.annotation  	Lifecycle kotlin.annotation  ListItem kotlin.annotation  Log kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  MutableStateFlow kotlin.annotation  OutlinedButton kotlin.annotation  OutlinedTextField kotlin.annotation  PasswordVisualTransformation kotlin.annotation  Project kotlin.annotation  ProjectRepository kotlin.annotation  
ProjectStream kotlin.annotation  ProjectTypeDropdown kotlin.annotation  ProjectsList kotlin.annotation  ProjectsUiState kotlin.annotation  Query kotlin.annotation  Row kotlin.annotation  Scaffold kotlin.annotation  SignInActivity kotlin.annotation  
SignInUiState kotlin.annotation  SingletonComponent kotlin.annotation  Source kotlin.annotation  Spacer kotlin.annotation  TAG kotlin.annotation  Text kotlin.annotation  	TextAlign kotlin.annotation  VisualTransformation kotlin.annotation  _state kotlin.annotation  _uiState kotlin.annotation  android kotlin.annotation  asStateFlow kotlin.annotation  await kotlin.annotation  callbackFlow kotlin.annotation  catch kotlin.annotation  collectAsState kotlin.annotation  com kotlin.annotation  db kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  finish kotlin.annotation  forEach kotlin.annotation  getValue kotlin.annotation  height kotlin.annotation  isBlank kotlin.annotation  isEmpty kotlin.annotation  
isNotEmpty kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  map kotlin.annotation  mapDocumentToProject kotlin.annotation  
mapNotNull kotlin.annotation  mapOf kotlin.annotation  padding kotlin.annotation  projectRepository kotlin.annotation  provideDelegate kotlin.annotation  repeatOnLifecycle kotlin.annotation  
repository kotlin.annotation  size kotlin.annotation  to kotlin.annotation  trim kotlin.annotation  	viewModel kotlin.annotation  AA103_POCTheme kotlin.collections  	Alignment kotlin.collections  Arrangement kotlin.collections  AuthGate kotlin.collections  Build kotlin.collections  Button kotlin.collections  CircularProgressIndicator kotlin.collections  CreateProjectActivity kotlin.collections  CreateProjectScreen kotlin.collections  CreateProjectUiState kotlin.collections  DropdownMenuItem kotlin.collections  
EmptyProjects kotlin.collections  	Exception kotlin.collections  ExperimentalMaterial3Api kotlin.collections  ExposedDropdownMenuDefaults kotlin.collections  
FieldValue kotlin.collections  Firebase kotlin.collections  FirebaseAuth kotlin.collections  FirebaseFirestore kotlin.collections  HorizontalDivider kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  IllegalStateException kotlin.collections  Intent kotlin.collections  JobTypeDropdown kotlin.collections  KeyboardOptions kotlin.collections  KeyboardType kotlin.collections  LaunchedEffect kotlin.collections  	Lifecycle kotlin.collections  List kotlin.collections  ListItem kotlin.collections  Log kotlin.collections  Map kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  MutableStateFlow kotlin.collections  OutlinedButton kotlin.collections  OutlinedTextField kotlin.collections  PasswordVisualTransformation kotlin.collections  Project kotlin.collections  ProjectRepository kotlin.collections  
ProjectStream kotlin.collections  ProjectTypeDropdown kotlin.collections  ProjectsList kotlin.collections  ProjectsUiState kotlin.collections  Query kotlin.collections  Row kotlin.collections  Scaffold kotlin.collections  SignInActivity kotlin.collections  
SignInUiState kotlin.collections  SingletonComponent kotlin.collections  Source kotlin.collections  Spacer kotlin.collections  TAG kotlin.collections  Text kotlin.collections  	TextAlign kotlin.collections  VisualTransformation kotlin.collections  _state kotlin.collections  _uiState kotlin.collections  android kotlin.collections  asStateFlow kotlin.collections  await kotlin.collections  callbackFlow kotlin.collections  catch kotlin.collections  collectAsState kotlin.collections  com kotlin.collections  db kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  finish kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  height kotlin.collections  isBlank kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  java kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapDocumentToProject kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  padding kotlin.collections  projectRepository kotlin.collections  provideDelegate kotlin.collections  repeatOnLifecycle kotlin.collections  
repository kotlin.collections  size kotlin.collections  to kotlin.collections  trim kotlin.collections  	viewModel kotlin.collections  getMAP kotlin.collections.MutableList  
getMAPNotNull kotlin.collections.MutableList  getMap kotlin.collections.MutableList  
getMapNotNull kotlin.collections.MutableList  AA103_POCTheme kotlin.comparisons  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  AuthGate kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  CreateProjectActivity kotlin.comparisons  CreateProjectScreen kotlin.comparisons  CreateProjectUiState kotlin.comparisons  DropdownMenuItem kotlin.comparisons  
EmptyProjects kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  ExposedDropdownMenuDefaults kotlin.comparisons  
FieldValue kotlin.comparisons  Firebase kotlin.comparisons  FirebaseAuth kotlin.comparisons  FirebaseFirestore kotlin.comparisons  HorizontalDivider kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  IllegalStateException kotlin.comparisons  Intent kotlin.comparisons  JobTypeDropdown kotlin.comparisons  KeyboardOptions kotlin.comparisons  KeyboardType kotlin.comparisons  LaunchedEffect kotlin.comparisons  	Lifecycle kotlin.comparisons  ListItem kotlin.comparisons  Log kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  MutableStateFlow kotlin.comparisons  OutlinedButton kotlin.comparisons  OutlinedTextField kotlin.comparisons  PasswordVisualTransformation kotlin.comparisons  Project kotlin.comparisons  ProjectRepository kotlin.comparisons  
ProjectStream kotlin.comparisons  ProjectTypeDropdown kotlin.comparisons  ProjectsList kotlin.comparisons  ProjectsUiState kotlin.comparisons  Query kotlin.comparisons  Row kotlin.comparisons  Scaffold kotlin.comparisons  SignInActivity kotlin.comparisons  
SignInUiState kotlin.comparisons  SingletonComponent kotlin.comparisons  Source kotlin.comparisons  Spacer kotlin.comparisons  TAG kotlin.comparisons  Text kotlin.comparisons  	TextAlign kotlin.comparisons  VisualTransformation kotlin.comparisons  _state kotlin.comparisons  _uiState kotlin.comparisons  android kotlin.comparisons  asStateFlow kotlin.comparisons  await kotlin.comparisons  callbackFlow kotlin.comparisons  catch kotlin.comparisons  collectAsState kotlin.comparisons  com kotlin.comparisons  db kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  finish kotlin.comparisons  forEach kotlin.comparisons  getValue kotlin.comparisons  height kotlin.comparisons  isBlank kotlin.comparisons  isEmpty kotlin.comparisons  
isNotEmpty kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  map kotlin.comparisons  mapDocumentToProject kotlin.comparisons  
mapNotNull kotlin.comparisons  mapOf kotlin.comparisons  padding kotlin.comparisons  projectRepository kotlin.comparisons  provideDelegate kotlin.comparisons  repeatOnLifecycle kotlin.comparisons  
repository kotlin.comparisons  size kotlin.comparisons  to kotlin.comparisons  trim kotlin.comparisons  	viewModel kotlin.comparisons  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  AA103_POCTheme 	kotlin.io  	Alignment 	kotlin.io  Arrangement 	kotlin.io  AuthGate 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  CircularProgressIndicator 	kotlin.io  CreateProjectActivity 	kotlin.io  CreateProjectScreen 	kotlin.io  CreateProjectUiState 	kotlin.io  DropdownMenuItem 	kotlin.io  
EmptyProjects 	kotlin.io  	Exception 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  ExposedDropdownMenuDefaults 	kotlin.io  
FieldValue 	kotlin.io  Firebase 	kotlin.io  FirebaseAuth 	kotlin.io  FirebaseFirestore 	kotlin.io  HorizontalDivider 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  IllegalStateException 	kotlin.io  Intent 	kotlin.io  JobTypeDropdown 	kotlin.io  KeyboardOptions 	kotlin.io  KeyboardType 	kotlin.io  LaunchedEffect 	kotlin.io  	Lifecycle 	kotlin.io  ListItem 	kotlin.io  Log 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  MutableStateFlow 	kotlin.io  OutlinedButton 	kotlin.io  OutlinedTextField 	kotlin.io  PasswordVisualTransformation 	kotlin.io  Project 	kotlin.io  ProjectRepository 	kotlin.io  
ProjectStream 	kotlin.io  ProjectTypeDropdown 	kotlin.io  ProjectsList 	kotlin.io  ProjectsUiState 	kotlin.io  Query 	kotlin.io  Row 	kotlin.io  Scaffold 	kotlin.io  SignInActivity 	kotlin.io  
SignInUiState 	kotlin.io  SingletonComponent 	kotlin.io  Source 	kotlin.io  Spacer 	kotlin.io  TAG 	kotlin.io  Text 	kotlin.io  	TextAlign 	kotlin.io  VisualTransformation 	kotlin.io  _state 	kotlin.io  _uiState 	kotlin.io  android 	kotlin.io  asStateFlow 	kotlin.io  await 	kotlin.io  callbackFlow 	kotlin.io  catch 	kotlin.io  collectAsState 	kotlin.io  com 	kotlin.io  db 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  finish 	kotlin.io  forEach 	kotlin.io  getValue 	kotlin.io  height 	kotlin.io  isBlank 	kotlin.io  isEmpty 	kotlin.io  
isNotEmpty 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  map 	kotlin.io  mapDocumentToProject 	kotlin.io  
mapNotNull 	kotlin.io  mapOf 	kotlin.io  padding 	kotlin.io  projectRepository 	kotlin.io  provideDelegate 	kotlin.io  repeatOnLifecycle 	kotlin.io  
repository 	kotlin.io  size 	kotlin.io  to 	kotlin.io  trim 	kotlin.io  	viewModel 	kotlin.io  AA103_POCTheme 
kotlin.jvm  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  AuthGate 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  CreateProjectActivity 
kotlin.jvm  CreateProjectScreen 
kotlin.jvm  CreateProjectUiState 
kotlin.jvm  DropdownMenuItem 
kotlin.jvm  
EmptyProjects 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  ExposedDropdownMenuDefaults 
kotlin.jvm  
FieldValue 
kotlin.jvm  Firebase 
kotlin.jvm  FirebaseAuth 
kotlin.jvm  FirebaseFirestore 
kotlin.jvm  HorizontalDivider 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  IllegalStateException 
kotlin.jvm  Intent 
kotlin.jvm  JobTypeDropdown 
kotlin.jvm  KeyboardOptions 
kotlin.jvm  KeyboardType 
kotlin.jvm  LaunchedEffect 
kotlin.jvm  	Lifecycle 
kotlin.jvm  ListItem 
kotlin.jvm  Log 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OutlinedButton 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  PasswordVisualTransformation 
kotlin.jvm  Project 
kotlin.jvm  ProjectRepository 
kotlin.jvm  
ProjectStream 
kotlin.jvm  ProjectTypeDropdown 
kotlin.jvm  ProjectsList 
kotlin.jvm  ProjectsUiState 
kotlin.jvm  Query 
kotlin.jvm  Row 
kotlin.jvm  Scaffold 
kotlin.jvm  SignInActivity 
kotlin.jvm  
SignInUiState 
kotlin.jvm  SingletonComponent 
kotlin.jvm  Source 
kotlin.jvm  Spacer 
kotlin.jvm  TAG 
kotlin.jvm  Text 
kotlin.jvm  	TextAlign 
kotlin.jvm  VisualTransformation 
kotlin.jvm  _state 
kotlin.jvm  _uiState 
kotlin.jvm  android 
kotlin.jvm  asStateFlow 
kotlin.jvm  await 
kotlin.jvm  callbackFlow 
kotlin.jvm  catch 
kotlin.jvm  collectAsState 
kotlin.jvm  com 
kotlin.jvm  db 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  finish 
kotlin.jvm  forEach 
kotlin.jvm  getValue 
kotlin.jvm  height 
kotlin.jvm  isBlank 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  map 
kotlin.jvm  mapDocumentToProject 
kotlin.jvm  
mapNotNull 
kotlin.jvm  mapOf 
kotlin.jvm  padding 
kotlin.jvm  projectRepository 
kotlin.jvm  provideDelegate 
kotlin.jvm  repeatOnLifecycle 
kotlin.jvm  
repository 
kotlin.jvm  size 
kotlin.jvm  to 
kotlin.jvm  trim 
kotlin.jvm  	viewModel 
kotlin.jvm  AA103_POCTheme 
kotlin.ranges  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  AuthGate 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  CreateProjectActivity 
kotlin.ranges  CreateProjectScreen 
kotlin.ranges  CreateProjectUiState 
kotlin.ranges  DropdownMenuItem 
kotlin.ranges  
EmptyProjects 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  ExposedDropdownMenuDefaults 
kotlin.ranges  
FieldValue 
kotlin.ranges  Firebase 
kotlin.ranges  FirebaseAuth 
kotlin.ranges  FirebaseFirestore 
kotlin.ranges  HorizontalDivider 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  IllegalStateException 
kotlin.ranges  Intent 
kotlin.ranges  JobTypeDropdown 
kotlin.ranges  KeyboardOptions 
kotlin.ranges  KeyboardType 
kotlin.ranges  LaunchedEffect 
kotlin.ranges  	Lifecycle 
kotlin.ranges  ListItem 
kotlin.ranges  Log 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OutlinedButton 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  PasswordVisualTransformation 
kotlin.ranges  Project 
kotlin.ranges  ProjectRepository 
kotlin.ranges  
ProjectStream 
kotlin.ranges  ProjectTypeDropdown 
kotlin.ranges  ProjectsList 
kotlin.ranges  ProjectsUiState 
kotlin.ranges  Query 
kotlin.ranges  Row 
kotlin.ranges  Scaffold 
kotlin.ranges  SignInActivity 
kotlin.ranges  
SignInUiState 
kotlin.ranges  SingletonComponent 
kotlin.ranges  Source 
kotlin.ranges  Spacer 
kotlin.ranges  TAG 
kotlin.ranges  Text 
kotlin.ranges  	TextAlign 
kotlin.ranges  VisualTransformation 
kotlin.ranges  _state 
kotlin.ranges  _uiState 
kotlin.ranges  android 
kotlin.ranges  asStateFlow 
kotlin.ranges  await 
kotlin.ranges  callbackFlow 
kotlin.ranges  catch 
kotlin.ranges  collectAsState 
kotlin.ranges  com 
kotlin.ranges  db 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  finish 
kotlin.ranges  forEach 
kotlin.ranges  getValue 
kotlin.ranges  height 
kotlin.ranges  isBlank 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  map 
kotlin.ranges  mapDocumentToProject 
kotlin.ranges  
mapNotNull 
kotlin.ranges  mapOf 
kotlin.ranges  padding 
kotlin.ranges  projectRepository 
kotlin.ranges  provideDelegate 
kotlin.ranges  repeatOnLifecycle 
kotlin.ranges  
repository 
kotlin.ranges  size 
kotlin.ranges  to 
kotlin.ranges  trim 
kotlin.ranges  	viewModel 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AA103_POCTheme kotlin.sequences  	Alignment kotlin.sequences  Arrangement kotlin.sequences  AuthGate kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  CircularProgressIndicator kotlin.sequences  CreateProjectActivity kotlin.sequences  CreateProjectScreen kotlin.sequences  CreateProjectUiState kotlin.sequences  DropdownMenuItem kotlin.sequences  
EmptyProjects kotlin.sequences  	Exception kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  ExposedDropdownMenuDefaults kotlin.sequences  
FieldValue kotlin.sequences  Firebase kotlin.sequences  FirebaseAuth kotlin.sequences  FirebaseFirestore kotlin.sequences  HorizontalDivider kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  IllegalStateException kotlin.sequences  Intent kotlin.sequences  JobTypeDropdown kotlin.sequences  KeyboardOptions kotlin.sequences  KeyboardType kotlin.sequences  LaunchedEffect kotlin.sequences  	Lifecycle kotlin.sequences  ListItem kotlin.sequences  Log kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  MutableStateFlow kotlin.sequences  OutlinedButton kotlin.sequences  OutlinedTextField kotlin.sequences  PasswordVisualTransformation kotlin.sequences  Project kotlin.sequences  ProjectRepository kotlin.sequences  
ProjectStream kotlin.sequences  ProjectTypeDropdown kotlin.sequences  ProjectsList kotlin.sequences  ProjectsUiState kotlin.sequences  Query kotlin.sequences  Row kotlin.sequences  Scaffold kotlin.sequences  SignInActivity kotlin.sequences  
SignInUiState kotlin.sequences  SingletonComponent kotlin.sequences  Source kotlin.sequences  Spacer kotlin.sequences  TAG kotlin.sequences  Text kotlin.sequences  	TextAlign kotlin.sequences  VisualTransformation kotlin.sequences  _state kotlin.sequences  _uiState kotlin.sequences  android kotlin.sequences  asStateFlow kotlin.sequences  await kotlin.sequences  callbackFlow kotlin.sequences  catch kotlin.sequences  collectAsState kotlin.sequences  com kotlin.sequences  db kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  finish kotlin.sequences  forEach kotlin.sequences  getValue kotlin.sequences  height kotlin.sequences  isBlank kotlin.sequences  isEmpty kotlin.sequences  
isNotEmpty kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  map kotlin.sequences  mapDocumentToProject kotlin.sequences  
mapNotNull kotlin.sequences  mapOf kotlin.sequences  padding kotlin.sequences  projectRepository kotlin.sequences  provideDelegate kotlin.sequences  repeatOnLifecycle kotlin.sequences  
repository kotlin.sequences  size kotlin.sequences  to kotlin.sequences  trim kotlin.sequences  	viewModel kotlin.sequences  AA103_POCTheme kotlin.text  	Alignment kotlin.text  Arrangement kotlin.text  AuthGate kotlin.text  Build kotlin.text  Button kotlin.text  CircularProgressIndicator kotlin.text  CreateProjectActivity kotlin.text  CreateProjectScreen kotlin.text  CreateProjectUiState kotlin.text  DropdownMenuItem kotlin.text  
EmptyProjects kotlin.text  	Exception kotlin.text  ExperimentalMaterial3Api kotlin.text  ExposedDropdownMenuDefaults kotlin.text  
FieldValue kotlin.text  Firebase kotlin.text  FirebaseAuth kotlin.text  FirebaseFirestore kotlin.text  HorizontalDivider kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  IllegalStateException kotlin.text  Intent kotlin.text  JobTypeDropdown kotlin.text  KeyboardOptions kotlin.text  KeyboardType kotlin.text  LaunchedEffect kotlin.text  	Lifecycle kotlin.text  ListItem kotlin.text  Log kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  MutableStateFlow kotlin.text  OutlinedButton kotlin.text  OutlinedTextField kotlin.text  PasswordVisualTransformation kotlin.text  Project kotlin.text  ProjectRepository kotlin.text  
ProjectStream kotlin.text  ProjectTypeDropdown kotlin.text  ProjectsList kotlin.text  ProjectsUiState kotlin.text  Query kotlin.text  Row kotlin.text  Scaffold kotlin.text  SignInActivity kotlin.text  
SignInUiState kotlin.text  SingletonComponent kotlin.text  Source kotlin.text  Spacer kotlin.text  TAG kotlin.text  Text kotlin.text  	TextAlign kotlin.text  VisualTransformation kotlin.text  _state kotlin.text  _uiState kotlin.text  android kotlin.text  asStateFlow kotlin.text  await kotlin.text  callbackFlow kotlin.text  catch kotlin.text  collectAsState kotlin.text  com kotlin.text  db kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  finish kotlin.text  forEach kotlin.text  getValue kotlin.text  height kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  java kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  map kotlin.text  mapDocumentToProject kotlin.text  
mapNotNull kotlin.text  mapOf kotlin.text  padding kotlin.text  projectRepository kotlin.text  provideDelegate kotlin.text  repeatOnLifecycle kotlin.text  
repository kotlin.text  size kotlin.text  to kotlin.text  trim kotlin.text  	viewModel kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  	Lifecycle !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  ProjectsUiState !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  _state !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  catch !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  getCATCH !kotlinx.coroutines.CoroutineScope  getCatch !kotlinx.coroutines.CoroutineScope  	getFINISH !kotlinx.coroutines.CoroutineScope  	getFinish !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getPROJECTRepository !kotlinx.coroutines.CoroutineScope  getProjectRepository !kotlinx.coroutines.CoroutineScope  getREPEATOnLifecycle !kotlinx.coroutines.CoroutineScope  
getREPOSITORY !kotlinx.coroutines.CoroutineScope  getRepeatOnLifecycle !kotlinx.coroutines.CoroutineScope  
getRepository !kotlinx.coroutines.CoroutineScope  getTRIM !kotlinx.coroutines.CoroutineScope  getTrim !kotlinx.coroutines.CoroutineScope  	get_state !kotlinx.coroutines.CoroutineScope  get_uiState !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  projectRepository !kotlinx.coroutines.CoroutineScope  repeatOnLifecycle !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  cancel kotlinx.coroutines.Job  isActive kotlinx.coroutines.Job  
ChannelResult kotlinx.coroutines.channels  
ProducerScope kotlinx.coroutines.channels  
awaitClose kotlinx.coroutines.channels  
ProjectStream )kotlinx.coroutines.channels.ProducerScope  Query )kotlinx.coroutines.channels.ProducerScope  Source )kotlinx.coroutines.channels.ProducerScope  await )kotlinx.coroutines.channels.ProducerScope  
awaitClose )kotlinx.coroutines.channels.ProducerScope  close )kotlinx.coroutines.channels.ProducerScope  db )kotlinx.coroutines.channels.ProducerScope  getAWAIT )kotlinx.coroutines.channels.ProducerScope  
getAWAITClose )kotlinx.coroutines.channels.ProducerScope  getAwait )kotlinx.coroutines.channels.ProducerScope  
getAwaitClose )kotlinx.coroutines.channels.ProducerScope  getDB )kotlinx.coroutines.channels.ProducerScope  getDb )kotlinx.coroutines.channels.ProducerScope  getMAP )kotlinx.coroutines.channels.ProducerScope  getMAPDocumentToProject )kotlinx.coroutines.channels.ProducerScope  getMap )kotlinx.coroutines.channels.ProducerScope  getMapDocumentToProject )kotlinx.coroutines.channels.ProducerScope  map )kotlinx.coroutines.channels.ProducerScope  mapDocumentToProject )kotlinx.coroutines.channels.ProducerScope  trySend )kotlinx.coroutines.channels.ProducerScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  catch kotlinx.coroutines.flow.Flow  collect kotlinx.coroutines.flow.Flow  getCATCH kotlinx.coroutines.flow.Flow  getCatch kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  ProjectsUiState %kotlinx.coroutines.flow.FlowCollector  _state %kotlinx.coroutines.flow.FlowCollector  	get_state %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsState !kotlinx.coroutines.flow.StateFlow  getCollectAsState !kotlinx.coroutines.flow.StateFlow  await kotlinx.coroutines.tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   