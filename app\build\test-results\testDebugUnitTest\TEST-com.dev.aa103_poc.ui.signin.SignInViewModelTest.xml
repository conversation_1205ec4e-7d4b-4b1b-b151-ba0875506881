<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.dev.aa103_poc.ui.signin.SignInViewModelTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-09-01T21:36:10" hostname="DESKTOP-R5PBK2U" time="0.008">
  <properties/>
  <testcase name="SignInUiState with error message should be correct" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.001"/>
  <testcase name="email validation logic should work correctly" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.001"/>
  <testcase name="SignInUiState copy should work correctly" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.001"/>
  <testcase name="SignInUiState with signed in state should be correct" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.0"/>
  <testcase name="SignInUiState with loading state should be correct" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.0"/>
  <testcase name="SignInUiState with password visibility should be correct" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.001"/>
  <testcase name="SignInUiState should have correct default values" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.002"/>
  <testcase name="password validation logic should work correctly" classname="com.dev.aa103_poc.ui.signin.SignInViewModelTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
