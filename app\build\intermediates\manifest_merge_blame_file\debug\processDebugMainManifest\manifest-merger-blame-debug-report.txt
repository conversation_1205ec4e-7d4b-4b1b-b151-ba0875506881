1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dev.aa103_poc"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
13-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:22-65
14    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
14-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
14-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
15-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
16-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
16-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
17    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
17-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
17-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
18    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
18-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
18-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
19
20    <permission
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-37:19
27        android:name="com.dev.aa103_poc.AA103Application"
27-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-41
28        android:allowBackup="true"
28-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-65
31        android:debuggable="true"
32        android:extractNativeLibs="false"
33        android:fullBackupContent="@xml/backup_rules"
33-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-54
34        android:icon="@mipmap/ic_launcher"
34-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-43
35        android:label="@string/app_name"
35-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-41
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-54
37        android:supportsRtl="true"
37-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-35
38        android:testOnly="true"
39        android:theme="@style/Theme.AA103_POC" >
39-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:14:9-47
40        <activity
40-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:9-26:20
41            android:name="com.dev.aa103_poc.MainActivity"
41-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-41
42            android:exported="true"
42-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-36
43            android:label="@string/app_name"
43-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-45
44            android:theme="@style/Theme.AA103_POC" >
44-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-51
45            <intent-filter>
45-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:13-25:29
46                <action android:name="android.intent.action.MAIN" />
46-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:22:17-69
46-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:22:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:24:17-77
48-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:24:27-74
49            </intent-filter>
50        </activity>
51        <activity
51-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:9-31:54
52            android:name="com.dev.aa103_poc.ui.signin.SignInActivity"
52-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-53
53            android:exported="false"
53-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-37
54            android:label="Sign In"
54-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-36
55            android:theme="@style/Theme.AA103_POC" />
55-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:31:13-51
56        <activity
56-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:32:9-36:54
57            android:name="com.dev.aa103_poc.ui.createproject.CreateProjectActivity"
57-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:33:13-67
58            android:exported="false"
58-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:34:13-37
59            android:label="Create Project"
59-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:35:13-43
60            android:theme="@style/Theme.AA103_POC" />
60-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:36:13-51
61
62        <service
62-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:8:9-14:19
63            android:name="com.google.firebase.components.ComponentDiscoveryService"
63-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:9:13-84
64            android:directBootAware="true"
64-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
65            android:exported="false" >
65-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:10:13-37
66            <meta-data
66-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:11:13-13:85
67                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
67-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:12:17-119
68                android:value="com.google.firebase.components.ComponentRegistrar" />
68-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:13:17-82
69            <meta-data
69-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
70                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
70-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
71                android:value="com.google.firebase.components.ComponentRegistrar" />
71-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
72            <meta-data
72-->[com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:12:13-14:85
73                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
73-->[com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:13:17-129
74                android:value="com.google.firebase.components.ComponentRegistrar" />
74-->[com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:14:17-82
75            <meta-data
75-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
76                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
76-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
77                android:value="com.google.firebase.components.ComponentRegistrar" />
77-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
78            <meta-data
78-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
79                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
79-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
80                android:value="com.google.firebase.components.ComponentRegistrar" />
80-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
81            <meta-data
81-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
82                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
82-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
83                android:value="com.google.firebase.components.ComponentRegistrar" />
83-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
84            <meta-data
84-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
85                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
85-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
86                android:value="com.google.firebase.components.ComponentRegistrar" />
86-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
87            <meta-data
87-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
88                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
88-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
89                android:value="com.google.firebase.components.ComponentRegistrar" />
89-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
90            <meta-data
90-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
91                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
91-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
93            <meta-data
93-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
94                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
94-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
96        </service>
97
98        <activity
98-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
99            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
99-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
100            android:excludeFromRecents="true"
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
101            android:exported="true"
101-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
102            android:launchMode="singleTask"
102-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
103            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
103-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
104            <intent-filter>
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
105                <action android:name="android.intent.action.VIEW" />
105-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
105-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
106
107                <category android:name="android.intent.category.DEFAULT" />
107-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
107-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
108                <category android:name="android.intent.category.BROWSABLE" />
108-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
108-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
109
110                <data
110-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
111                    android:host="firebase.auth"
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
112                    android:path="/"
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
113                    android:scheme="genericidp" />
113-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
114            </intent-filter>
115        </activity>
116        <activity
116-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
117            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
117-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
118            android:excludeFromRecents="true"
118-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
119            android:exported="true"
119-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
120            android:launchMode="singleTask"
120-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
121            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
121-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
122            <intent-filter>
122-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
123                <action android:name="android.intent.action.VIEW" />
123-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
123-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
124
125                <category android:name="android.intent.category.DEFAULT" />
125-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
125-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
126                <category android:name="android.intent.category.BROWSABLE" />
126-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
126-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
127
128                <data
128-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
129                    android:host="firebase.auth"
129-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
130                    android:path="/"
130-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
131                    android:scheme="recaptcha" />
131-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
132            </intent-filter>
133        </activity>
134
135        <service
135-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
136            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
136-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
137            android:enabled="true"
137-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
138            android:exported="false" >
138-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
139            <meta-data
139-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
140                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
140-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
141                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
141-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
142        </service>
143
144        <activity
144-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
145            android:name="androidx.credentials.playservices.HiddenActivity"
145-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
146            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
146-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
147            android:enabled="true"
147-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
148            android:exported="false"
148-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
149            android:fitsSystemWindows="true"
149-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
150            android:theme="@style/Theme.Hidden" >
150-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
151        </activity>
152        <activity
152-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
153            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
153-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
154            android:excludeFromRecents="true"
154-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
155            android:exported="false"
155-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
156-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
157        <!--
158            Service handling Google Sign-In user revocation. For apps that do not integrate with
159            Google Sign-In, this service will never be started.
160        -->
161        <service
161-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
162            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
162-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
163            android:exported="true"
163-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
164            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
164-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
165            android:visibleToInstantApps="true" />
165-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
166
167        <activity
167-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
168            android:name="com.google.android.gms.common.api.GoogleApiActivity"
168-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
169            android:exported="false"
169-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
170            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
170-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
171
172        <receiver
172-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
173            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
173-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
174            android:enabled="true"
174-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
175            android:exported="false" >
175-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
176        </receiver>
177
178        <service
178-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
179            android:name="com.google.android.gms.measurement.AppMeasurementService"
179-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
180            android:enabled="true"
180-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
181            android:exported="false" />
181-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
182        <service
182-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
183            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
183-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
184            android:enabled="true"
184-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
185            android:exported="false"
185-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
186            android:permission="android.permission.BIND_JOB_SERVICE" />
186-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
187
188        <property
188-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
189            android:name="android.adservices.AD_SERVICES_CONFIG"
189-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
190            android:resource="@xml/ga_ad_services_config" />
190-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
191
192        <provider
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
193            android:name="com.google.firebase.provider.FirebaseInitProvider"
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
194            android:authorities="com.dev.aa103_poc.firebaseinitprovider"
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
195            android:directBootAware="true"
195-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
196            android:exported="false"
196-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
197            android:initOrder="100" />
197-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
198
199        <activity
199-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
200            android:name="androidx.activity.ComponentActivity"
200-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
201            android:exported="true" />
201-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
202        <activity
202-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
203            android:name="androidx.compose.ui.tooling.PreviewActivity"
203-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
204            android:exported="true" />
204-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
205
206        <provider
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
207            android:name="androidx.startup.InitializationProvider"
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
208            android:authorities="com.dev.aa103_poc.androidx-startup"
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
209            android:exported="false" >
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
210            <meta-data
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.emoji2.text.EmojiCompatInitializer"
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
212                android:value="androidx.startup" />
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
213            <meta-data
213-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
214                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
214-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
215                android:value="androidx.startup" />
215-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
216            <meta-data
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
217                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
218                android:value="androidx.startup" />
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
219        </provider>
220
221        <uses-library
221-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:23:9-25:40
222            android:name="androidx.window.extensions"
222-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:24:13-54
223            android:required="false" />
223-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:25:13-37
224        <uses-library
224-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:26:9-28:40
225            android:name="androidx.window.sidecar"
225-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:27:13-51
226            android:required="false" />
226-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:28:13-37
227        <uses-library
227-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
228            android:name="android.ext.adservices"
228-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
229            android:required="false" />
229-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
230
231        <meta-data
231-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
232            android:name="com.google.android.gms.version"
232-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
233            android:value="@integer/google_play_services_version" />
233-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
234
235        <receiver
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
236            android:name="androidx.profileinstaller.ProfileInstallReceiver"
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
237            android:directBootAware="false"
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
238            android:enabled="true"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
239            android:exported="true"
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
240            android:permission="android.permission.DUMP" >
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
241            <intent-filter>
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
242                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
243            </intent-filter>
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
245                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
246            </intent-filter>
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
248                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
251                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
252            </intent-filter>
253        </receiver>
254    </application>
255
256</manifest>
