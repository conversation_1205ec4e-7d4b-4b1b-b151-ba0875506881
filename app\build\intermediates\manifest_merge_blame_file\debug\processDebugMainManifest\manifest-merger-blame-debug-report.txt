1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dev.aa103_poc"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
13-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:22-65
14    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
14-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
14-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
15-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
16-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
16-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
17    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
17-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
17-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
18    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
18-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\24a49126bb18c9b96a314e1c8686f42e\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
18-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\24a49126bb18c9b96a314e1c8686f42e\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
19
20    <permission
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-36:19
27        android:allowBackup="true"
27-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-65
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:fullBackupContent="@xml/backup_rules"
32-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-54
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-41
35        android:roundIcon="@mipmap/ic_launcher_round"
35-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-54
36        android:supportsRtl="true"
36-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-35
37        android:testOnly="true"
38        android:theme="@style/Theme.AA103_POC" >
38-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-47
39        <activity
39-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-25:20
40            android:name="com.dev.aa103_poc.MainActivity"
40-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:13-41
41            android:exported="true"
41-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-36
42            android:label="@string/app_name"
42-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-45
43            android:theme="@style/Theme.AA103_POC" >
43-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-51
44            <intent-filter>
44-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-24:29
45                <action android:name="android.intent.action.MAIN" />
45-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:17-69
45-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:17-77
47-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:27-74
48            </intent-filter>
49        </activity>
50        <activity
50-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:26:9-30:54
51            android:name="com.dev.aa103_poc.ui.signin.SignInActivity"
51-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:13-53
52            android:exported="false"
52-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-37
53            android:label="Sign In"
53-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-36
54            android:theme="@style/Theme.AA103_POC" />
54-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-51
55        <activity
55-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:31:9-35:54
56            android:name="com.dev.aa103_poc.ui.createproject.CreateProjectActivity"
56-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:32:13-67
57            android:exported="false"
57-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:33:13-37
58            android:label="Create Project"
58-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:34:13-43
59            android:theme="@style/Theme.AA103_POC" />
59-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:35:13-51
60
61        <service
61-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:8:9-14:19
62            android:name="com.google.firebase.components.ComponentDiscoveryService"
62-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:9:13-84
63            android:directBootAware="true"
63-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
64            android:exported="false" >
64-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:10:13-37
65            <meta-data
65-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:11:13-13:85
66                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
66-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:12:17-119
67                android:value="com.google.firebase.components.ComponentRegistrar" />
67-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:13:17-82
68            <meta-data
68-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
69                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
69-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
70                android:value="com.google.firebase.components.ComponentRegistrar" />
70-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
71            <meta-data
71-->[com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4512e8db039ab5001b76d9f3175ed8ef\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:12:13-14:85
72                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
72-->[com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4512e8db039ab5001b76d9f3175ed8ef\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:13:17-129
73                android:value="com.google.firebase.components.ComponentRegistrar" />
73-->[com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4512e8db039ab5001b76d9f3175ed8ef\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:14:17-82
74            <meta-data
74-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\87ac57de64e6fb467407237da4e255b1\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
75                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
75-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\87ac57de64e6fb467407237da4e255b1\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
76                android:value="com.google.firebase.components.ComponentRegistrar" />
76-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\87ac57de64e6fb467407237da4e255b1\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
77            <meta-data
77-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\87ac57de64e6fb467407237da4e255b1\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
78                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
78-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\87ac57de64e6fb467407237da4e255b1\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
79                android:value="com.google.firebase.components.ComponentRegistrar" />
79-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\87ac57de64e6fb467407237da4e255b1\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
80            <meta-data
80-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
81                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
81-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
82                android:value="com.google.firebase.components.ComponentRegistrar" />
82-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
83            <meta-data
83-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
84                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
84-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
85                android:value="com.google.firebase.components.ComponentRegistrar" />
85-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
86            <meta-data
86-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
87                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
87-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
88                android:value="com.google.firebase.components.ComponentRegistrar" />
88-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
89            <meta-data
89-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\793200600de3b11375042a8058e75108\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
90                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
90-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\793200600de3b11375042a8058e75108\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
91                android:value="com.google.firebase.components.ComponentRegistrar" />
91-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\793200600de3b11375042a8058e75108\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
92            <meta-data
92-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
93                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
93-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
95        </service>
96
97        <activity
97-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
98            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
98-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
99            android:excludeFromRecents="true"
99-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
100            android:exported="true"
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
101            android:launchMode="singleTask"
101-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
102            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
102-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
103            <intent-filter>
103-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
104                <action android:name="android.intent.action.VIEW" />
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
105
106                <category android:name="android.intent.category.DEFAULT" />
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
107                <category android:name="android.intent.category.BROWSABLE" />
107-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
107-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
108
109                <data
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
110                    android:host="firebase.auth"
110-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
111                    android:path="/"
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
112                    android:scheme="genericidp" />
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
113            </intent-filter>
114        </activity>
115        <activity
115-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
116            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
116-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
117            android:excludeFromRecents="true"
117-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
118            android:exported="true"
118-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
119            android:launchMode="singleTask"
119-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
120            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
120-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
121            <intent-filter>
121-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
122                <action android:name="android.intent.action.VIEW" />
122-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
122-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
123
124                <category android:name="android.intent.category.DEFAULT" />
124-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
124-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
125                <category android:name="android.intent.category.BROWSABLE" />
125-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
125-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
126
127                <data
127-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
128                    android:host="firebase.auth"
128-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
129                    android:path="/"
129-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
130                    android:scheme="recaptcha" />
130-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
131            </intent-filter>
132        </activity>
133
134        <service
134-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
135            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
135-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
136            android:enabled="true"
136-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
137            android:exported="false" >
137-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
138            <meta-data
138-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
139                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
139-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
140                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
140-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
141        </service>
142
143        <activity
143-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
144            android:name="androidx.credentials.playservices.HiddenActivity"
144-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
145            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
145-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
146            android:enabled="true"
146-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
147            android:exported="false"
147-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
148            android:fitsSystemWindows="true"
148-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
149            android:theme="@style/Theme.Hidden" >
149-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
150        </activity>
151        <activity
151-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
152            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
152-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
153            android:excludeFromRecents="true"
153-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
154            android:exported="false"
154-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
155            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
155-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
156        <!--
157            Service handling Google Sign-In user revocation. For apps that do not integrate with
158            Google Sign-In, this service will never be started.
159        -->
160        <service
160-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
161            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
161-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
162            android:exported="true"
162-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
163            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
163-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
164            android:visibleToInstantApps="true" />
164-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
165
166        <activity
166-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
167            android:name="com.google.android.gms.common.api.GoogleApiActivity"
167-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
168            android:exported="false"
168-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
169            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
169-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
170
171        <receiver
171-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
172            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
172-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
173            android:enabled="true"
173-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
174            android:exported="false" >
174-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
175        </receiver>
176
177        <service
177-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
178            android:name="com.google.android.gms.measurement.AppMeasurementService"
178-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
179            android:enabled="true"
179-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
180            android:exported="false" />
180-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
181        <service
181-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
182            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
182-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
183            android:enabled="true"
183-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
184            android:exported="false"
184-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
185            android:permission="android.permission.BIND_JOB_SERVICE" />
185-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
186
187        <property
187-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
188            android:name="android.adservices.AD_SERVICES_CONFIG"
188-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
189            android:resource="@xml/ga_ad_services_config" />
189-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
190
191        <provider
191-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
192            android:name="com.google.firebase.provider.FirebaseInitProvider"
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
193            android:authorities="com.dev.aa103_poc.firebaseinitprovider"
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
194            android:directBootAware="true"
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
195            android:exported="false"
195-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
196            android:initOrder="100" />
196-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
197
198        <activity
198-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\70e767629cf1af451f0870d444132579\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
199            android:name="androidx.activity.ComponentActivity"
199-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\70e767629cf1af451f0870d444132579\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
200            android:exported="true" />
200-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\70e767629cf1af451f0870d444132579\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
201        <activity
201-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6e0541a7b2fa9f2c82d39edc162fc881\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
202            android:name="androidx.compose.ui.tooling.PreviewActivity"
202-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6e0541a7b2fa9f2c82d39edc162fc881\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
203            android:exported="true" />
203-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\6e0541a7b2fa9f2c82d39edc162fc881\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
204
205        <provider
205-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
206            android:name="androidx.startup.InitializationProvider"
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
207            android:authorities="com.dev.aa103_poc.androidx-startup"
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
208            android:exported="false" >
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
209            <meta-data
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
210                android:name="androidx.emoji2.text.EmojiCompatInitializer"
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
211                android:value="androidx.startup" />
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
212            <meta-data
212-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\86b55738124e10db4ab5aca31d5c7001\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
213                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
213-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\86b55738124e10db4ab5aca31d5c7001\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
214                android:value="androidx.startup" />
214-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\86b55738124e10db4ab5aca31d5c7001\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
215            <meta-data
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
216                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
217                android:value="androidx.startup" />
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
218        </provider>
219
220        <uses-library
220-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\7b16836c0fecc34e938dff4e2a236ac3\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
221            android:name="android.ext.adservices"
221-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\7b16836c0fecc34e938dff4e2a236ac3\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
222            android:required="false" />
222-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\7b16836c0fecc34e938dff4e2a236ac3\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
223
224        <meta-data
224-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc3f110c8971a71c5a78795c57b75cdc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
225            android:name="com.google.android.gms.version"
225-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc3f110c8971a71c5a78795c57b75cdc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
226            android:value="@integer/google_play_services_version" />
226-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc3f110c8971a71c5a78795c57b75cdc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
227
228        <receiver
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
229            android:name="androidx.profileinstaller.ProfileInstallReceiver"
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
230            android:directBootAware="false"
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
231            android:enabled="true"
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
232            android:exported="true"
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
233            android:permission="android.permission.DUMP" >
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
235                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
236            </intent-filter>
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
238                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
241                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
244                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
245            </intent-filter>
246        </receiver>
247    </application>
248
249</manifest>
