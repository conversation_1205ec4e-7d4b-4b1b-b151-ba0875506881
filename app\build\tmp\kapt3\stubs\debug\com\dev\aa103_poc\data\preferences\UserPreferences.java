package com.dev.aa103_poc.data.preferences;

/**
 * Manages user preferences for the application
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\r\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/dev/aa103_poc/data/preferences/UserPreferences;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "prefs", "Landroid/content/SharedPreferences;", "clearUserPreferences", "", "uid", "", "isFirstLogin", "", "markFirstSyncComplete", "app_debug"})
public final class UserPreferences {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences prefs = null;
    
    @javax.inject.Inject()
    public UserPreferences(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Check if this is the first login for a specific user
     * @param uid User ID
     * @return true if first login, false otherwise
     */
    public final boolean isFirstLogin(@org.jetbrains.annotations.NotNull()
    java.lang.String uid) {
        return false;
    }
    
    /**
     * Mark that the user has completed their first sync
     * @param uid User ID
     */
    public final void markFirstSyncComplete(@org.jetbrains.annotations.NotNull()
    java.lang.String uid) {
    }
    
    /**
     * Clear preferences for a user (e.g., on logout)
     * @param uid User ID
     */
    public final void clearUserPreferences(@org.jetbrains.annotations.NotNull()
    java.lang.String uid) {
    }
}