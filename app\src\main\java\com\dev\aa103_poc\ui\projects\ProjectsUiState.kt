package com.dev.aa103_poc.ui.projects

import com.dev.aa103_poc.data.model.Project

/**
 * UI state for the Projects screen
 * Represents different states of project loading and display
 */
sealed interface ProjectsUiState {
    /**
     * Initial loading state when first subscribing to projects
     */
    data object Loading : ProjectsUiState
    
    /**
     * Content state when projects are available
     * @param projects List of projects to display
     * @param fromCache Whether the data is served from local cache
     * @param isRefreshing Whether the user is currently refreshing
     */
    data class Content(
        val projects: List<Project>,
        val fromCache: Boolean,
        val isRefreshing: Boolean = false
    ) : ProjectsUiState
    
    /**
     * Empty state when user has no projects
     */
    data object Empty : ProjectsUiState
    
    /**
     * Error state when something goes wrong
     * @param message Error message to display
     */
    data class Error(val message: String) : ProjectsUiState
}
