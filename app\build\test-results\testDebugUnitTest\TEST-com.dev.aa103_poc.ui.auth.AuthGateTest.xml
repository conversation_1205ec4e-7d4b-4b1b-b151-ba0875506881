<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.dev.aa103_poc.ui.auth.AuthGateTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-09-03T02:14:07" hostname="DESKTOP-R5PBK2U" time="0.009">
  <properties/>
  <testcase name="password validation - empty password should fail" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.0"/>
  <testcase name="password validation - non-empty password should pass" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.0"/>
  <testcase name="email validation - valid emails should pass" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.001"/>
  <testcase name="authentication state logic - null user should require sign in" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.0"/>
  <testcase name="authentication error handling - should provide user-friendly messages" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.005"/>
  <testcase name="authentication flow - sign out should clear user" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.001"/>
  <testcase name="email validation - invalid emails should fail" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.001"/>
  <testcase name="authentication state logic - non-null user should show signed in content" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.0"/>
  <testcase name="authentication flow - successful sign in should set user" classname="com.dev.aa103_poc.ui.auth.AuthGateTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
