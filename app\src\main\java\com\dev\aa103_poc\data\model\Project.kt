package com.dev.aa103_poc.data.model

import com.google.firebase.Timestamp

/**
 * Data model for a project
 * Represents a project stored in Firestore at users/{uid}/projects/{projectId}
 */
data class Project(
    val id: String = "",
    val title: String = "",
    val type: String = "",
    val jobType: String = "",
    val ownerUid: String = "",
    val createdAt: Timestamp? = null,
    val updatedAt: Timestamp? = null
) {
    companion object {
        // Available project types
        const val TYPE_WALL = "Wall"
        
        // Available job types
        const val JOB_TYPE_PAINTING = "Painting job"
        
        // Get list of available types
        fun getAvailableTypes(): List<String> = listOf(TYPE_WALL)
        
        // Get list of available job types
        fun getAvailableJobTypes(): List<String> = listOf(JOB_TYPE_PAINTING)
    }
}
