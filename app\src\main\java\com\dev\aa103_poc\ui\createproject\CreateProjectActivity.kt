package com.dev.aa103_poc.ui.createproject

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.dev.aa103_poc.data.model.Project
import com.dev.aa103_poc.ui.theme.AA103_POCTheme

class CreateProjectActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            AA103_POCTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    val viewModel: CreateProjectViewModel = viewModel()
                    val uiState by viewModel.uiState.collectAsState()
                    
                    // Handle successful project creation
                    LaunchedEffect(uiState.isProjectCreated) {
                        if (uiState.isProjectCreated) {
                            // Project created successfully, finish activity
                            finish()
                        }
                    }
                    
                    CreateProjectScreen(
                        modifier = Modifier.padding(innerPadding),
                        uiState = uiState,
                        onTitleChange = viewModel::updateTitle,
                        onTypeChange = viewModel::updateType,
                        onJobTypeChange = viewModel::updateJobType,
                        onCreateProject = viewModel::createProject,
                        onCancel = { finish() },
                        onErrorDismiss = viewModel::clearError
                    )
                }
            }
        }
    }
}

@Composable
fun CreateProjectScreen(
    modifier: Modifier = Modifier,
    uiState: CreateProjectUiState,
    onTitleChange: (String) -> Unit,
    onTypeChange: (String) -> Unit,
    onJobTypeChange: (String) -> Unit,
    onCreateProject: () -> Unit,
    onCancel: () -> Unit,
    onErrorDismiss: () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Title
        Text(
            text = "Create New Project",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Project Title Input
        OutlinedTextField(
            value = uiState.title,
            onValueChange = {
                onTitleChange(it)
                if (uiState.errorMessage != null) {
                    onErrorDismiss()
                }
            },
            label = { Text("Project Title") },
            placeholder = { Text("Enter project title") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            isError = uiState.errorMessage != null
        )

        // Project Type Dropdown
        ProjectTypeDropdown(
            selectedType = uiState.selectedType,
            onTypeSelected = onTypeChange,
            modifier = Modifier.fillMaxWidth()
        )

        // Job Type Dropdown
        JobTypeDropdown(
            selectedJobType = uiState.selectedJobType,
            onJobTypeSelected = onJobTypeChange,
            modifier = Modifier.fillMaxWidth()
        )

        // Error message
        if (uiState.errorMessage != null) {
            Text(
                text = uiState.errorMessage,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.fillMaxWidth()
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // Action Buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Cancel Button
            OutlinedButton(
                onClick = onCancel,
                modifier = Modifier.weight(1f),
                enabled = !uiState.isLoading
            ) {
                Text("Cancel")
            }

            // Create Button
            Button(
                onClick = onCreateProject,
                modifier = Modifier.weight(1f),
                enabled = !uiState.isLoading && uiState.title.trim().isNotEmpty()
            ) {
                if (uiState.isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("Create Project")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProjectTypeDropdown(
    selectedType: String,
    onTypeSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    val types = Project.getAvailableTypes()

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded },
        modifier = modifier
    ) {
        OutlinedTextField(
            value = selectedType,
            onValueChange = { },
            readOnly = true,
            label = { Text("Project Type") },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor()
        )

        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            types.forEach { type ->
                DropdownMenuItem(
                    text = { Text(type) },
                    onClick = {
                        onTypeSelected(type)
                        expanded = false
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JobTypeDropdown(
    selectedJobType: String,
    onJobTypeSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    val jobTypes = Project.getAvailableJobTypes()

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded },
        modifier = modifier
    ) {
        OutlinedTextField(
            value = selectedJobType,
            onValueChange = { },
            readOnly = true,
            label = { Text("Job Type") },
            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor()
        )

        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            jobTypes.forEach { jobType ->
                DropdownMenuItem(
                    text = { Text(jobType) },
                    onClick = {
                        onJobTypeSelected(jobType)
                        expanded = false
                    }
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CreateProjectScreenPreview() {
    AA103_POCTheme {
        CreateProjectScreen(
            uiState = CreateProjectUiState(
                title = "Sample Project",
                selectedType = Project.TYPE_WALL,
                selectedJobType = Project.JOB_TYPE_PAINTING
            ),
            onTitleChange = {},
            onTypeChange = {},
            onJobTypeChange = {},
            onCreateProject = {},
            onCancel = {},
            onErrorDismiss = {}
        )
    }
}
