package com.dev.aa103_poc.ui.projects

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.dev.aa103_poc.data.repository.ProjectRepositoryInterface
import com.google.firebase.auth.FirebaseAuth
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Projects screen
 * Handles streaming project data and managing UI state
 */
@HiltViewModel
class ProjectsViewModel @Inject constructor(
    private val repository: ProjectRepositoryInterface,
    private val auth: FirebaseAuth
) : ViewModel() {

    private val _state = MutableStateFlow<ProjectsUiState>(ProjectsUiState.Loading)
    val state: StateFlow<ProjectsUiState> = _state

    private var streamingJob: Job? = null

    /**
     * Start streaming projects for the current user
     * This should be called from UI with proper lifecycle management
     */
    fun startStreaming() {
        // Don't start if already streaming
        if (streamingJob?.isActive == true) return

        val uid = auth.currentUser?.uid
        if (uid == null) {
            _state.value = ProjectsUiState.Error("Not signed in")
            return
        }

        streamingJob = viewModelScope.launch {
            repository.stream(uid)
                .catch { error ->
                    _state.value = ProjectsUiState.Error("Failed to load projects: ${error.message}")
                }
                .collect { projectStream ->
                    if (projectStream.items.isEmpty()) {
                        _state.value = ProjectsUiState.Empty
                    } else {
                        _state.value = ProjectsUiState.Content(
                            projects = projectStream.items,
                            fromCache = projectStream.fromCache,
                            syncing = !projectStream.fromCache || projectStream.hasPendingWrites
                        )
                    }
                }
        }
    }

    /**
     * Stop streaming projects
     * Called automatically when ViewModel is cleared
     */
    fun stopStreaming() {
        streamingJob?.cancel()
        streamingJob = null
    }

    override fun onCleared() {
        super.onCleared()
        stopStreaming()
    }
}
