package com.dev.aa103_poc.ui.projects

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.dev.aa103_poc.data.model.ProjectStream
import com.dev.aa103_poc.data.preferences.UserPreferences
import com.dev.aa103_poc.data.repository.ProjectRepositoryInterface
import com.google.firebase.auth.FirebaseAuth
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Projects screen
 * Handles manual loading and refreshing of project data
 */
@HiltViewModel
class ProjectsViewModel @Inject constructor(
    private val repository: ProjectRepositoryInterface,
    private val auth: FirebaseAuth,
    private val userPreferences: UserPreferences
) : ViewModel() {

    private val _state = MutableStateFlow<ProjectsUiState>(ProjectsUiState.Loading)
    val state: StateFlow<ProjectsUiState> = _state

    init {
        loadProjects()
    }

    /**
     * Load projects from cache (called on initialization)
     * If this is the first login, also sync from server
     */
    fun loadProjects() {
        val uid = auth.currentUser?.uid
        if (uid == null) {
            _state.value = ProjectsUiState.Error("Not signed in")
            return
        }

        viewModelScope.launch {
            try {
                // Always load from cache first
                val projectStream = repository.loadFromCache(uid)
                updateStateFromProjectStream(projectStream)

                // If this is the first login, sync from server
                if (userPreferences.isFirstLogin(uid)) {
                    try {
                        val serverStream = repository.refreshFromServer(uid)
                        updateStateFromProjectStream(serverStream)
                        userPreferences.markFirstSyncComplete(uid)
                    } catch (e: Exception) {
                        // First sync failed, but we still have cache data
                        // Don't mark as complete so it will retry next time
                    }
                }
            } catch (e: Exception) {
                _state.value = ProjectsUiState.Error("Failed to load projects: ${e.message}")
            }
        }
    }

    /**
     * Refresh projects from server (called by pull-to-refresh)
     */
    fun refreshProjects() {
        val uid = auth.currentUser?.uid
        if (uid == null) {
            _state.value = ProjectsUiState.Error("Not signed in")
            return
        }

        // Set refreshing state only if we have content
        val currentState = _state.value
        if (currentState is ProjectsUiState.Content) {
            _state.value = currentState.copy(isRefreshing = true)
        }

        viewModelScope.launch {
            try {
                val projectStream = repository.refreshFromServer(uid)
                updateStateFromProjectStream(projectStream, isRefreshing = false)
            } catch (e: Exception) {
                // On error, stop refreshing but keep current content
                val existingState = _state.value
                when (existingState) {
                    is ProjectsUiState.Content -> {
                        _state.value = existingState.copy(isRefreshing = false)
                    }
                    ProjectsUiState.Empty -> {
                        // Keep empty state, don't show error for refresh failure
                        _state.value = ProjectsUiState.Empty
                    }
                    else -> {
                        _state.value = ProjectsUiState.Error("Failed to refresh projects: ${e.message}")
                    }
                }
            }
        }
    }

    private fun updateStateFromProjectStream(projectStream: ProjectStream, isRefreshing: Boolean = false) {
        if (projectStream.items.isEmpty()) {
            _state.value = ProjectsUiState.Empty
        } else {
            _state.value = ProjectsUiState.Content(
                projects = projectStream.items,
                fromCache = projectStream.fromCache,
                isRefreshing = isRefreshing
            )
        }
    }
}
