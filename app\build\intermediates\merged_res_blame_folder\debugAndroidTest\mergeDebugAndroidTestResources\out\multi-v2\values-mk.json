{"logs": [{"outputFile": "com.dev.aa103_poc.test.app-mergeDebugAndroidTestResources-1:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,500,605,708,2031", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "198,300,397,495,600,703,819,2127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1393,1464", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1388,1459,1580"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,928,1018,1114,1217,1302,1379,1469,1561,1645,1716,1786,1870,1959,2132,2213,2284", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "923,1013,1109,1212,1297,1374,1464,1556,1640,1711,1781,1865,1954,2026,2208,2279,2400"}}]}]}