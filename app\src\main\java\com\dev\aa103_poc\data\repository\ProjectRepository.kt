package com.dev.aa103_poc.data.repository

import com.dev.aa103_poc.data.model.Project
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await

/**
 * Repository class for managing project data in Firestore
 * Handles CRUD operations for projects stored at users/{uid}/projects/{projectId}
 */
class ProjectRepository {
    
    private val firestore = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    
    /**
     * Get the projects collection reference for the current user
     * @return Collection reference for users/{uid}/projects
     * @throws IllegalStateException if no user is signed in
     */
    private fun getProjectsCollection() = 
        auth.currentUser?.uid?.let { uid ->
            firestore.collection("users").document(uid).collection("projects")
        } ?: throw IllegalStateException("No user signed in")
    
    /**
     * Create a new project in Firestore
     * @param title The project title
     * @param type The project type (e.g., "Wall")
     * @param jobType The job type (e.g., "Painting job")
     * @return The auto-generated project ID
     * @throws IllegalStateException if no user is signed in
     */
    suspend fun createProject(title: String, type: String, jobType: String): String {
        val uid = auth.currentUser?.uid 
            ?: throw IllegalStateException("No user signed in")
        
        val projectsCol = getProjectsCollection()
        val docRef = projectsCol.document() // Creates a new doc ref with auto-ID
        
        val data = mapOf(
            "id" to docRef.id,
            "title" to title.trim(),
            "type" to type,
            "jobType" to jobType,
            "ownerUid" to uid,
            "createdAt" to FieldValue.serverTimestamp(),
            "updatedAt" to FieldValue.serverTimestamp()
        )
        
        docRef.set(data).await()
        return docRef.id
    }
    
    /**
     * Get all projects for the current user
     * @return List of projects
     * @throws IllegalStateException if no user is signed in
     */
    suspend fun getProjects(): List<Project> {
        val projectsCol = getProjectsCollection()
        val snapshot = projectsCol.get().await()
        
        return snapshot.documents.mapNotNull { doc ->
            doc.toObject(Project::class.java)
        }
    }
}
