  AuthenticationUnitTest com.dev.aa103_poc  Boolean com.dev.aa103_poc  String com.dev.aa103_poc  Boolean (com.dev.aa103_poc.AuthenticationUnitTest  String (com.dev.aa103_poc.AuthenticationUnitTest  Test (com.dev.aa103_poc.AuthenticationUnitTest  AuthGateTest com.dev.aa103_poc.ui.auth  Boolean com.dev.aa103_poc.ui.auth  String com.dev.aa103_poc.ui.auth  Boolean &com.dev.aa103_poc.ui.auth.AuthGateTest  String &com.dev.aa103_poc.ui.auth.AuthGateTest  Test &com.dev.aa103_poc.ui.auth.AuthGateTest  String 7com.dev.aa103_poc.ui.auth.AuthGateTest.MockFirebaseUser  Boolean com.dev.aa103_poc.ui.signin  
SignInUiState com.dev.aa103_poc.ui.signin  SignInViewModelTest com.dev.aa103_poc.ui.signin  String com.dev.aa103_poc.ui.signin  Boolean /com.dev.aa103_poc.ui.signin.SignInViewModelTest  String /com.dev.aa103_poc.ui.signin.SignInViewModelTest  Test /com.dev.aa103_poc.ui.signin.SignInViewModelTest  Boolean kotlin  String kotlin  Assert 	org.junit  Test 	org.junit  assertEquals org.junit.Assert  assertFalse org.junit.Assert  
assertNull org.junit.Assert  
assertTrue org.junit.Assert                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          