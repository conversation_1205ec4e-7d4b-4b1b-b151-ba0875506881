package com.dev.aa103_poc.ui.projects;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a \u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\u0016\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0003\u001a\u0012\u0010\n\u001a\u00020\u00012\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\u0010\u0010\r\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\fH\u0007\u001a\b\u0010\u000e\u001a\u00020\u0001H\u0007\u00a8\u0006\u000f"}, d2 = {"EmptyProjects", "", "modifier", "Landroidx/compose/ui/Modifier;", "onAdd", "Lkotlin/Function0;", "ProjectsList", "projects", "", "Lcom/dev/aa103_poc/data/model/Project;", "ProjectsRoute", "viewModel", "Lcom/dev/aa103_poc/ui/projects/ProjectsViewModel;", "ProjectsScreen", "ProjectsScreenPreview", "app_debug"})
public final class ProjectsScreenKt {
    
    /**
     * Route for Projects screen
     * No longer needs lifecycle management since we use manual refresh
     */
    @androidx.compose.runtime.Composable()
    public static final void ProjectsRoute(@org.jetbrains.annotations.NotNull()
    com.dev.aa103_poc.ui.projects.ProjectsViewModel viewModel) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void ProjectsScreen(@org.jetbrains.annotations.NotNull()
    com.dev.aa103_poc.ui.projects.ProjectsViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmptyProjects(androidx.compose.ui.Modifier modifier, kotlin.jvm.functions.Function0<kotlin.Unit> onAdd) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ProjectsList(java.util.List<com.dev.aa103_poc.data.model.Project> projects) {
    }
    
    @androidx.compose.ui.tooling.preview.Preview(showBackground = true)
    @androidx.compose.runtime.Composable()
    public static final void ProjectsScreenPreview() {
    }
}