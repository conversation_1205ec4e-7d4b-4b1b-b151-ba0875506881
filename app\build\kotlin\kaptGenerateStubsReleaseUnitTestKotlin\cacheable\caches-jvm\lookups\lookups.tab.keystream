  AuthenticationUnitTest com.dev.aa103_poc  Boolean com.dev.aa103_poc  String com.dev.aa103_poc  Boolean (com.dev.aa103_poc.AuthenticationUnitTest  String (com.dev.aa103_poc.AuthenticationUnitTest  Test (com.dev.aa103_poc.AuthenticationUnitTest  Project com.dev.aa103_poc.data.model  
ProjectStream com.dev.aa103_poc.data.model  invoke .com.dev.aa103_poc.data.model.Project.Companion  ProjectRepositoryInterface !com.dev.aa103_poc.data.repository  stream <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  AuthGateTest com.dev.aa103_poc.ui.auth  Boolean com.dev.aa103_poc.ui.auth  String com.dev.aa103_poc.ui.auth  Boolean &com.dev.aa103_poc.ui.auth.AuthGateTest  String &com.dev.aa103_poc.ui.auth.AuthGateTest  Test &com.dev.aa103_poc.ui.auth.AuthGateTest  String 7com.dev.aa103_poc.ui.auth.AuthGateTest.MockFirebaseUser  ExperimentalCoroutinesApi com.dev.aa103_poc.ui.projects  OptIn com.dev.aa103_poc.ui.projects  Project com.dev.aa103_poc.ui.projects  
ProjectStream com.dev.aa103_poc.ui.projects  ProjectsUiState com.dev.aa103_poc.ui.projects  ProjectsViewModel com.dev.aa103_poc.ui.projects  ProjectsViewModelTest com.dev.aa103_poc.ui.projects  StandardTestDispatcher com.dev.aa103_poc.ui.projects  assertEquals com.dev.aa103_poc.ui.projects  
assertTrue com.dev.aa103_poc.ui.projects  auth com.dev.aa103_poc.ui.projects  	emptyList com.dev.aa103_poc.ui.projects  flowOf com.dev.aa103_poc.ui.projects  listOf com.dev.aa103_poc.ui.projects  
repository com.dev.aa103_poc.ui.projects  runTest com.dev.aa103_poc.ui.projects  testDispatcher com.dev.aa103_poc.ui.projects  user com.dev.aa103_poc.ui.projects  whenever com.dev.aa103_poc.ui.projects  Content -com.dev.aa103_poc.ui.projects.ProjectsUiState  Empty -com.dev.aa103_poc.ui.projects.ProjectsUiState  Error -com.dev.aa103_poc.ui.projects.ProjectsUiState  	fromCache -com.dev.aa103_poc.ui.projects.ProjectsUiState  message -com.dev.aa103_poc.ui.projects.ProjectsUiState  projects -com.dev.aa103_poc.ui.projects.ProjectsUiState  syncing -com.dev.aa103_poc.ui.projects.ProjectsUiState  	fromCache 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  projects 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  syncing 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  message 3com.dev.aa103_poc.ui.projects.ProjectsUiState.Error  state /com.dev.aa103_poc.ui.projects.ProjectsViewModel  After 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Before 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  FirebaseAuth 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  FirebaseUser 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Mock 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Project 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  ProjectRepositoryInterface 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
ProjectStream 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  ProjectsUiState 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  ProjectsViewModel 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  StandardTestDispatcher 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Test 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  assertEquals 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
assertTrue 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  auth 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	emptyList 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  flowOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getASSERTEquals 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getASSERTTrue 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getAssertEquals 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getAssertTrue 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getEMPTYList 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getEmptyList 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getFLOWOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getFlowOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getLISTOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getListOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getRUNTest 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getRunTest 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getWHENEVER 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getWhenever 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  invoke 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  listOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
repository 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  runTest 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  testDispatcher 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  user 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  whenever 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Boolean com.dev.aa103_poc.ui.signin  
SignInUiState com.dev.aa103_poc.ui.signin  SignInViewModelTest com.dev.aa103_poc.ui.signin  String com.dev.aa103_poc.ui.signin  Boolean /com.dev.aa103_poc.ui.signin.SignInViewModelTest  String /com.dev.aa103_poc.ui.signin.SignInViewModelTest  Test /com.dev.aa103_poc.ui.signin.SignInViewModelTest  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  currentUser %com.google.firebase.auth.FirebaseAuth  getCURRENTUser %com.google.firebase.auth.FirebaseAuth  getCurrentUser %com.google.firebase.auth.FirebaseAuth  setCurrentUser %com.google.firebase.auth.FirebaseAuth  getUID %com.google.firebase.auth.FirebaseUser  getUid %com.google.firebase.auth.FirebaseUser  setUid %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  ExperimentalCoroutinesApi 	java.lang  Project 	java.lang  
ProjectStream 	java.lang  ProjectsViewModel 	java.lang  StandardTestDispatcher 	java.lang  assertEquals 	java.lang  
assertTrue 	java.lang  auth 	java.lang  	emptyList 	java.lang  flowOf 	java.lang  listOf 	java.lang  
repository 	java.lang  runTest 	java.lang  testDispatcher 	java.lang  user 	java.lang  whenever 	java.lang  Boolean kotlin  ExperimentalCoroutinesApi kotlin  Nothing kotlin  OptIn kotlin  Project kotlin  
ProjectStream kotlin  ProjectsViewModel kotlin  StandardTestDispatcher kotlin  String kotlin  assertEquals kotlin  
assertTrue kotlin  auth kotlin  	emptyList kotlin  flowOf kotlin  listOf kotlin  
repository kotlin  runTest kotlin  testDispatcher kotlin  user kotlin  whenever kotlin  ExperimentalCoroutinesApi kotlin.annotation  Project kotlin.annotation  
ProjectStream kotlin.annotation  ProjectsViewModel kotlin.annotation  StandardTestDispatcher kotlin.annotation  assertEquals kotlin.annotation  
assertTrue kotlin.annotation  auth kotlin.annotation  	emptyList kotlin.annotation  flowOf kotlin.annotation  listOf kotlin.annotation  
repository kotlin.annotation  runTest kotlin.annotation  testDispatcher kotlin.annotation  user kotlin.annotation  whenever kotlin.annotation  ExperimentalCoroutinesApi kotlin.collections  List kotlin.collections  Project kotlin.collections  
ProjectStream kotlin.collections  ProjectsViewModel kotlin.collections  StandardTestDispatcher kotlin.collections  assertEquals kotlin.collections  
assertTrue kotlin.collections  auth kotlin.collections  	emptyList kotlin.collections  flowOf kotlin.collections  listOf kotlin.collections  
repository kotlin.collections  runTest kotlin.collections  testDispatcher kotlin.collections  user kotlin.collections  whenever kotlin.collections  ExperimentalCoroutinesApi kotlin.comparisons  Project kotlin.comparisons  
ProjectStream kotlin.comparisons  ProjectsViewModel kotlin.comparisons  StandardTestDispatcher kotlin.comparisons  assertEquals kotlin.comparisons  
assertTrue kotlin.comparisons  auth kotlin.comparisons  	emptyList kotlin.comparisons  flowOf kotlin.comparisons  listOf kotlin.comparisons  
repository kotlin.comparisons  runTest kotlin.comparisons  testDispatcher kotlin.comparisons  user kotlin.comparisons  whenever kotlin.comparisons  SuspendFunction1 kotlin.coroutines  advanceUntilIdle 1kotlin.coroutines.AbstractCoroutineContextElement  ExperimentalCoroutinesApi 	kotlin.io  Project 	kotlin.io  
ProjectStream 	kotlin.io  ProjectsViewModel 	kotlin.io  StandardTestDispatcher 	kotlin.io  assertEquals 	kotlin.io  
assertTrue 	kotlin.io  auth 	kotlin.io  	emptyList 	kotlin.io  flowOf 	kotlin.io  listOf 	kotlin.io  
repository 	kotlin.io  runTest 	kotlin.io  testDispatcher 	kotlin.io  user 	kotlin.io  whenever 	kotlin.io  ExperimentalCoroutinesApi 
kotlin.jvm  Project 
kotlin.jvm  
ProjectStream 
kotlin.jvm  ProjectsViewModel 
kotlin.jvm  StandardTestDispatcher 
kotlin.jvm  assertEquals 
kotlin.jvm  
assertTrue 
kotlin.jvm  auth 
kotlin.jvm  	emptyList 
kotlin.jvm  flowOf 
kotlin.jvm  listOf 
kotlin.jvm  
repository 
kotlin.jvm  runTest 
kotlin.jvm  testDispatcher 
kotlin.jvm  user 
kotlin.jvm  whenever 
kotlin.jvm  ExperimentalCoroutinesApi 
kotlin.ranges  Project 
kotlin.ranges  
ProjectStream 
kotlin.ranges  ProjectsViewModel 
kotlin.ranges  StandardTestDispatcher 
kotlin.ranges  assertEquals 
kotlin.ranges  
assertTrue 
kotlin.ranges  auth 
kotlin.ranges  	emptyList 
kotlin.ranges  flowOf 
kotlin.ranges  listOf 
kotlin.ranges  
repository 
kotlin.ranges  runTest 
kotlin.ranges  testDispatcher 
kotlin.ranges  user 
kotlin.ranges  whenever 
kotlin.ranges  KClass kotlin.reflect  ExperimentalCoroutinesApi kotlin.sequences  Project kotlin.sequences  
ProjectStream kotlin.sequences  ProjectsViewModel kotlin.sequences  StandardTestDispatcher kotlin.sequences  assertEquals kotlin.sequences  
assertTrue kotlin.sequences  auth kotlin.sequences  	emptyList kotlin.sequences  flowOf kotlin.sequences  listOf kotlin.sequences  
repository kotlin.sequences  runTest kotlin.sequences  testDispatcher kotlin.sequences  user kotlin.sequences  whenever kotlin.sequences  assertEquals kotlin.test  
assertTrue kotlin.test  ExperimentalCoroutinesApi kotlin.text  Project kotlin.text  
ProjectStream kotlin.text  ProjectsViewModel kotlin.text  StandardTestDispatcher kotlin.text  assertEquals kotlin.text  
assertTrue kotlin.text  auth kotlin.text  	emptyList kotlin.text  flowOf kotlin.text  listOf kotlin.text  
repository kotlin.text  runTest kotlin.text  testDispatcher kotlin.text  user kotlin.text  whenever kotlin.text  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  Flow kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  value !kotlinx.coroutines.flow.StateFlow  StandardTestDispatcher kotlinx.coroutines.test  TestDispatcher kotlinx.coroutines.test  	TestScope kotlinx.coroutines.test  	resetMain kotlinx.coroutines.test  runTest kotlinx.coroutines.test  setMain kotlinx.coroutines.test  advanceUntilIdle .kotlinx.coroutines.test.TestCoroutineScheduler  	scheduler &kotlinx.coroutines.test.TestDispatcher  Project !kotlinx.coroutines.test.TestScope  
ProjectStream !kotlinx.coroutines.test.TestScope  ProjectsViewModel !kotlinx.coroutines.test.TestScope  assertEquals !kotlinx.coroutines.test.TestScope  
assertTrue !kotlinx.coroutines.test.TestScope  auth !kotlinx.coroutines.test.TestScope  	emptyList !kotlinx.coroutines.test.TestScope  flowOf !kotlinx.coroutines.test.TestScope  getASSERTEquals !kotlinx.coroutines.test.TestScope  
getASSERTTrue !kotlinx.coroutines.test.TestScope  getAUTH !kotlinx.coroutines.test.TestScope  getAssertEquals !kotlinx.coroutines.test.TestScope  
getAssertTrue !kotlinx.coroutines.test.TestScope  getAuth !kotlinx.coroutines.test.TestScope  getEMPTYList !kotlinx.coroutines.test.TestScope  getEmptyList !kotlinx.coroutines.test.TestScope  	getFLOWOf !kotlinx.coroutines.test.TestScope  	getFlowOf !kotlinx.coroutines.test.TestScope  	getLISTOf !kotlinx.coroutines.test.TestScope  	getListOf !kotlinx.coroutines.test.TestScope  
getREPOSITORY !kotlinx.coroutines.test.TestScope  
getRepository !kotlinx.coroutines.test.TestScope  getTESTDispatcher !kotlinx.coroutines.test.TestScope  getTestDispatcher !kotlinx.coroutines.test.TestScope  getUSER !kotlinx.coroutines.test.TestScope  getUser !kotlinx.coroutines.test.TestScope  getWHENEVER !kotlinx.coroutines.test.TestScope  getWhenever !kotlinx.coroutines.test.TestScope  invoke !kotlinx.coroutines.test.TestScope  listOf !kotlinx.coroutines.test.TestScope  
repository !kotlinx.coroutines.test.TestScope  testDispatcher !kotlinx.coroutines.test.TestScope  user !kotlinx.coroutines.test.TestScope  whenever !kotlinx.coroutines.test.TestScope  After 	org.junit  Assert 	org.junit  Before 	org.junit  Test 	org.junit  assertEquals org.junit.Assert  assertFalse org.junit.Assert  
assertNull org.junit.Assert  
assertTrue org.junit.Assert  Mock org.mockito  MockitoAnnotations org.mockito  whenever org.mockito.kotlin  OngoingStubbing org.mockito.stubbing  
thenReturn $org.mockito.stubbing.OngoingStubbing                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         