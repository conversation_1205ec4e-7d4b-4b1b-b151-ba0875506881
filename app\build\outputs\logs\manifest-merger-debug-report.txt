-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-39:12
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:1-39:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7c0aa032a843398057284b4aa1035a69\transformed\hilt-navigation-compose-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.hilt:hilt-navigation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\513731724061499c82992633717654dc\transformed\hilt-navigation-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\ed8278c6be7e200d0f58676897c8931c\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:2:1-26:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\d3a7801405f4d72120d05d7dea20d496\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9d3547f9a8cb20124510a9e088d12ed7\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98c04fab3297e6ebfcb8d7a337f24db4\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\22d60374a7ee72fff9cc7fb4ea243789\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71b5ba4e736017d8a8975d3305a907bd\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\815ed57be87de2b996a7c342e928fc1f\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9599791a069045fb8741b71212be6c39\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\dcf409e1c172b789350a4582dd4c4c4e\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b31f5c63cf441ad48f99ace8972d83e\transformed\integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49e178ad8243c1edd9272b22c071f6dc\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2f216ec89c740e7f554936dad71daf24\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f6c8a172184eda6559766ea04031e59\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\1ee94ca273be4a4aa6367a40b44f1ea0\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.compose.material3:material3-adaptive-navigation-suite-android:1.0.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\a1d543136b4a429e8268b7ff5375602a\transformed\material3-adaptive-navigation-suite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-adaptive-android:1.0.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-4\0787108143dd8408ebcc0f344ce0f819\transformed\material3-adaptive-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\115151fc6581ba5bce5a93703f671592\transformed\material3-window-size-class-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4bccd5606ba1f722682e9c63c366f5bf\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8dd648e43b3149d2cfa4c946f09671ed\transformed\navigation-compose-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3f756b428178d4d97c7fc676d8c13022\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4f7b4d93006b9392e16b48ec5d5b62e4\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b9d14c671fbb7db970f032040916dd29\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5ade2daabe3be4a9f3ec8278e02e3f12\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e6bc97ad0782d829d3d5e198b6e4fba6\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0e6d3aea59b03a582db1662700fd8712\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb057246c5b679a2b26b83faf8d3226f\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\64d1623d530d67b7029efe1f2f178a6f\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c9d11bd66462f2f4391bca7e94a0781a\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4f98f45b46d8843ab8aad9b675bf0b42\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e02f241f9d459d00728f2ac3c08a0d1b\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\da81f05fce552b38d741dcb733a13f60\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\45300a2913df88807723c4190777e013\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1c29391f4a765fdb02f934bc74996395\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a409b1441504cc1188980a3cd9ae60d8\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d53e3bae34564b1121d74df56b771890\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e50cf9992e3f84f75678288bd32ffd3\transformed\navigation-runtime-ktx-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cefdc79400af01ee96d40a4907365265\transformed\navigation-runtime-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common-ktx:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\50655693e99d0301232bfb87e3329d96\transformed\navigation-common-ktx-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\050cc1ef6f2a2d973bccca489c96cbdc\transformed\navigation-common-2.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\603084b8eccd7975e4dd2fad567213a0\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0abe3a6b529e21d7dd841223f1dfdac6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\209d97372c834c393c11aaebed6e3490\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8626df688f61d5ac498bc1bba03693ce\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\d7fd9c3403d3813cc7b1456efa4ea4de\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\1edf7750772e15a4e7e282ee3c52d40d\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\15c62e4a6b4ad6c2b101bfa18369d847\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\9745ba946685dbad84077bac085b6d0d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b143239b82cf782ddd1a03f79809b6d9\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\92351d6ef4fba333cf446a76ff5479fc\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\24032e911a6762c1c0a7e77ed171784a\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\fea96be97fc2c6ca15b7b731b11df655\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\434e6b3dbcaf34e63f06f652ea7d3dcd\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\872bbc2e484e3fdb32724ceddcaf44aa\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\789178468d917c5aad09a685dd59740b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4fb5569ccdb16db285bb3ca6b3f0aa2c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d39eab54189f912db4ae077b344789\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd22806efa89fd4e339f9b2dc02f092a\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b94e814f709946e827c700093c04fc9f\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\7129be592996363953f7732f7b93aed9\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\1680c4d067181f94e95cbaca41f10947\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\48127b829acf9e3a2697ec3974b843b9\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\70e3b5e3daa2cca868b20f72a0842174\transformed\activity-ktx-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\7410dcd99eed14a67f7281b0232f67a1\transformed\activity-compose-1.9.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\6ea528116884d6f2cb33292a8ad9e376\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7737a164322ddcfaf310e17c3ae3dd04\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d50c4a5ec120a979e953307fdcd395a7\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\134fa655109773dcee3a849ea977633f\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be1b2d2a6efc09774e73f8be3db079ed\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3003d5cffc968eefc6e7a108e33ffd4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\9dc51da9e1ffe9f78f8a79e6387146a3\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\768170fbe9f73dc6f9a59ef104002441\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\640b65914af4d8ad64af7eda36754469\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d3b0e255c84a4ee0d67c49751ae1b85\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7477fc5342f7262bf308d3d07d39af05\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\03126432af00ffaf14dc924d17c71325\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bea5b7a692e33546695f4eda025ecd3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fd965b233ffb19923c2aba9163faef9\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1cfb4e4089d03811d90e994c5c50f303\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\27b3f81200311fdb885e03e0a3ec7a3f\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\593a87db5701ecee08000decc5b14bfe\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\607f08000d57ee37d1f53e9f1e02f7d3\transformed\core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\77dda2692a47f5e50e843701fc0ff952\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\1136b53f4e42c2fdea34e0348dc06353\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f012e423c42ece3ff3fcf50424dbcdf\transformed\grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e64a0f23f578aa698b999b4e57ef44e\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-37:19
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-37:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:13:5-24:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98c04fab3297e6ebfcb8d7a337f24db4\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98c04fab3297e6ebfcb8d7a337f24db4\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71b5ba4e736017d8a8975d3305a907bd\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71b5ba4e736017d8a8975d3305a907bd\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9599791a069045fb8741b71212be6c39\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9599791a069045fb8741b71212be6c39\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\dcf409e1c172b789350a4582dd4c4c4e\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\dcf409e1c172b789350a4582dd4c4c4e\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b31f5c63cf441ad48f99ace8972d83e\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b31f5c63cf441ad48f99ace8972d83e\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49e178ad8243c1edd9272b22c071f6dc\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49e178ad8243c1edd9272b22c071f6dc\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2f216ec89c740e7f554936dad71daf24\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2f216ec89c740e7f554936dad71daf24\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f6c8a172184eda6559766ea04031e59\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f6c8a172184eda6559766ea04031e59\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\1ee94ca273be4a4aa6367a40b44f1ea0\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\1ee94ca273be4a4aa6367a40b44f1ea0\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4fb5569ccdb16db285bb3ca6b3f0aa2c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4fb5569ccdb16db285bb3ca6b3f0aa2c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d39eab54189f912db4ae077b344789\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d39eab54189f912db4ae077b344789\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\03126432af00ffaf14dc924d17c71325\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\03126432af00ffaf14dc924d17c71325\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bea5b7a692e33546695f4eda025ecd3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bea5b7a692e33546695f4eda025ecd3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-29
	android:icon
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:14:9-47
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-65
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-41
activity#com.dev.aa103_poc.MainActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:9-26:20
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-45
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-36
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:13-25:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:22:17-69
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:24:27-74
activity#com.dev.aa103_poc.ui.signin.SignInActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:9-31:54
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-36
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:31:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-53
activity#com.dev.aa103_poc.ui.createproject.CreateProjectActivity
ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:32:9-36:54
	android:label
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:35:13-43
	android:exported
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:36:13-51
	android:name
		ADDED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:33:13-67
uses-sdk
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7c0aa032a843398057284b4aa1035a69\transformed\hilt-navigation-compose-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.hilt:hilt-navigation-compose:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7c0aa032a843398057284b4aa1035a69\transformed\hilt-navigation-compose-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.hilt:hilt-navigation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\513731724061499c82992633717654dc\transformed\hilt-navigation-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.hilt:hilt-navigation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\513731724061499c82992633717654dc\transformed\hilt-navigation-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\ed8278c6be7e200d0f58676897c8931c\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\ed8278c6be7e200d0f58676897c8931c\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:6:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\d3a7801405f4d72120d05d7dea20d496\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\d3a7801405f4d72120d05d7dea20d496\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9d3547f9a8cb20124510a9e088d12ed7\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9d3547f9a8cb20124510a9e088d12ed7\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98c04fab3297e6ebfcb8d7a337f24db4\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98c04fab3297e6ebfcb8d7a337f24db4\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\22d60374a7ee72fff9cc7fb4ea243789\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\22d60374a7ee72fff9cc7fb4ea243789\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71b5ba4e736017d8a8975d3305a907bd\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\71b5ba4e736017d8a8975d3305a907bd\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\815ed57be87de2b996a7c342e928fc1f\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\.gradle\caches\transforms-4\815ed57be87de2b996a7c342e928fc1f\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9599791a069045fb8741b71212be6c39\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\9599791a069045fb8741b71212be6c39\transformed\firebase-analytics-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\dcf409e1c172b789350a4582dd4c4c4e\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\dcf409e1c172b789350a4582dd4c4c4e\transformed\play-services-measurement-sdk-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b31f5c63cf441ad48f99ace8972d83e\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\0b31f5c63cf441ad48f99ace8972d83e\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49e178ad8243c1edd9272b22c071f6dc\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49e178ad8243c1edd9272b22c071f6dc\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2f216ec89c740e7f554936dad71daf24\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2f216ec89c740e7f554936dad71daf24\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f6c8a172184eda6559766ea04031e59\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f6c8a172184eda6559766ea04031e59\transformed\play-services-measurement-base-22.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\1ee94ca273be4a4aa6367a40b44f1ea0\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\1ee94ca273be4a4aa6367a40b44f1ea0\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-adaptive-navigation-suite-android:1.0.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\a1d543136b4a429e8268b7ff5375602a\transformed\material3-adaptive-navigation-suite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-adaptive-navigation-suite-android:1.0.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\a1d543136b4a429e8268b7ff5375602a\transformed\material3-adaptive-navigation-suite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-adaptive-android:1.0.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-4\0787108143dd8408ebcc0f344ce0f819\transformed\material3-adaptive-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-adaptive-android:1.0.0-alpha03] C:\Users\<USER>\.gradle\caches\transforms-4\0787108143dd8408ebcc0f344ce0f819\transformed\material3-adaptive-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\115151fc6581ba5bce5a93703f671592\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\115151fc6581ba5bce5a93703f671592\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4bccd5606ba1f722682e9c63c366f5bf\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4bccd5606ba1f722682e9c63c366f5bf\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8dd648e43b3149d2cfa4c946f09671ed\transformed\navigation-compose-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\8dd648e43b3149d2cfa4c946f09671ed\transformed\navigation-compose-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3f756b428178d4d97c7fc676d8c13022\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\3f756b428178d4d97c7fc676d8c13022\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4f7b4d93006b9392e16b48ec5d5b62e4\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4f7b4d93006b9392e16b48ec5d5b62e4\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b9d14c671fbb7db970f032040916dd29\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\b9d14c671fbb7db970f032040916dd29\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5ade2daabe3be4a9f3ec8278e02e3f12\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\5ade2daabe3be4a9f3ec8278e02e3f12\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e6bc97ad0782d829d3d5e198b6e4fba6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e6bc97ad0782d829d3d5e198b6e4fba6\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0e6d3aea59b03a582db1662700fd8712\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\0e6d3aea59b03a582db1662700fd8712\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb057246c5b679a2b26b83faf8d3226f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb057246c5b679a2b26b83faf8d3226f\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\64d1623d530d67b7029efe1f2f178a6f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\64d1623d530d67b7029efe1f2f178a6f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c9d11bd66462f2f4391bca7e94a0781a\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\c9d11bd66462f2f4391bca7e94a0781a\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4f98f45b46d8843ab8aad9b675bf0b42\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\4f98f45b46d8843ab8aad9b675bf0b42\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e02f241f9d459d00728f2ac3c08a0d1b\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e02f241f9d459d00728f2ac3c08a0d1b\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\da81f05fce552b38d741dcb733a13f60\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\da81f05fce552b38d741dcb733a13f60\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\45300a2913df88807723c4190777e013\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\45300a2913df88807723c4190777e013\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1c29391f4a765fdb02f934bc74996395\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\1c29391f4a765fdb02f934bc74996395\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a409b1441504cc1188980a3cd9ae60d8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\a409b1441504cc1188980a3cd9ae60d8\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d53e3bae34564b1121d74df56b771890\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\d53e3bae34564b1121d74df56b771890\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e50cf9992e3f84f75678288bd32ffd3\transformed\navigation-runtime-ktx-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-ktx:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6e50cf9992e3f84f75678288bd32ffd3\transformed\navigation-runtime-ktx-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cefdc79400af01ee96d40a4907365265\transformed\navigation-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\cefdc79400af01ee96d40a4907365265\transformed\navigation-runtime-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\50655693e99d0301232bfb87e3329d96\transformed\navigation-common-ktx-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\50655693e99d0301232bfb87e3329d96\transformed\navigation-common-ktx-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\050cc1ef6f2a2d973bccca489c96cbdc\transformed\navigation-common-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\050cc1ef6f2a2d973bccca489c96cbdc\transformed\navigation-common-2.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\603084b8eccd7975e4dd2fad567213a0\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\603084b8eccd7975e4dd2fad567213a0\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0abe3a6b529e21d7dd841223f1dfdac6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0abe3a6b529e21d7dd841223f1dfdac6\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\209d97372c834c393c11aaebed6e3490\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\209d97372c834c393c11aaebed6e3490\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8626df688f61d5ac498bc1bba03693ce\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\8626df688f61d5ac498bc1bba03693ce\transformed\lifecycle-livedata-core-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\d7fd9c3403d3813cc7b1456efa4ea4de\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\d7fd9c3403d3813cc7b1456efa4ea4de\transformed\lifecycle-livedata-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\1edf7750772e15a4e7e282ee3c52d40d\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\1edf7750772e15a4e7e282ee3c52d40d\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\15c62e4a6b4ad6c2b101bfa18369d847\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\15c62e4a6b4ad6c2b101bfa18369d847\transformed\lifecycle-viewmodel-2.8.6\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\9745ba946685dbad84077bac085b6d0d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\9745ba946685dbad84077bac085b6d0d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b143239b82cf782ddd1a03f79809b6d9\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b143239b82cf782ddd1a03f79809b6d9\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\92351d6ef4fba333cf446a76ff5479fc\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\92351d6ef4fba333cf446a76ff5479fc\transformed\lifecycle-livedata-core-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\24032e911a6762c1c0a7e77ed171784a\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\24032e911a6762c1c0a7e77ed171784a\transformed\lifecycle-viewmodel-ktx-2.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\fea96be97fc2c6ca15b7b731b11df655\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\fea96be97fc2c6ca15b7b731b11df655\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\434e6b3dbcaf34e63f06f652ea7d3dcd\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-4\434e6b3dbcaf34e63f06f652ea7d3dcd\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\872bbc2e484e3fdb32724ceddcaf44aa\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\872bbc2e484e3fdb32724ceddcaf44aa\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\789178468d917c5aad09a685dd59740b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\789178468d917c5aad09a685dd59740b\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4fb5569ccdb16db285bb3ca6b3f0aa2c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\4fb5569ccdb16db285bb3ca6b3f0aa2c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d39eab54189f912db4ae077b344789\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\55d39eab54189f912db4ae077b344789\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd22806efa89fd4e339f9b2dc02f092a\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-4\fd22806efa89fd4e339f9b2dc02f092a\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b94e814f709946e827c700093c04fc9f\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\b94e814f709946e827c700093c04fc9f\transformed\lifecycle-viewmodel-savedstate-2.8.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\7129be592996363953f7732f7b93aed9\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\7129be592996363953f7732f7b93aed9\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\1680c4d067181f94e95cbaca41f10947\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\1680c4d067181f94e95cbaca41f10947\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\48127b829acf9e3a2697ec3974b843b9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\48127b829acf9e3a2697ec3974b843b9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\70e3b5e3daa2cca868b20f72a0842174\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\70e3b5e3daa2cca868b20f72a0842174\transformed\activity-ktx-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\7410dcd99eed14a67f7281b0232f67a1\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\7410dcd99eed14a67f7281b0232f67a1\transformed\activity-compose-1.9.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\6ea528116884d6f2cb33292a8ad9e376\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\transforms-4\6ea528116884d6f2cb33292a8ad9e376\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7737a164322ddcfaf310e17c3ae3dd04\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\7737a164322ddcfaf310e17c3ae3dd04\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d50c4a5ec120a979e953307fdcd395a7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d50c4a5ec120a979e953307fdcd395a7\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\134fa655109773dcee3a849ea977633f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\134fa655109773dcee3a849ea977633f\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be1b2d2a6efc09774e73f8be3db079ed\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\be1b2d2a6efc09774e73f8be3db079ed\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3003d5cffc968eefc6e7a108e33ffd4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3003d5cffc968eefc6e7a108e33ffd4\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\9dc51da9e1ffe9f78f8a79e6387146a3\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\9dc51da9e1ffe9f78f8a79e6387146a3\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\768170fbe9f73dc6f9a59ef104002441\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\768170fbe9f73dc6f9a59ef104002441\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\640b65914af4d8ad64af7eda36754469\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\640b65914af4d8ad64af7eda36754469\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d3b0e255c84a4ee0d67c49751ae1b85\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d3b0e255c84a4ee0d67c49751ae1b85\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7477fc5342f7262bf308d3d07d39af05\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7477fc5342f7262bf308d3d07d39af05\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\03126432af00ffaf14dc924d17c71325\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\03126432af00ffaf14dc924d17c71325\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bea5b7a692e33546695f4eda025ecd3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bea5b7a692e33546695f4eda025ecd3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fd965b233ffb19923c2aba9163faef9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fd965b233ffb19923c2aba9163faef9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1cfb4e4089d03811d90e994c5c50f303\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1cfb4e4089d03811d90e994c5c50f303\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\27b3f81200311fdb885e03e0a3ec7a3f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\27b3f81200311fdb885e03e0a3ec7a3f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\593a87db5701ecee08000decc5b14bfe\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\593a87db5701ecee08000decc5b14bfe\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\607f08000d57ee37d1f53e9f1e02f7d3\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\607f08000d57ee37d1f53e9f1e02f7d3\transformed\core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\77dda2692a47f5e50e843701fc0ff952\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\77dda2692a47f5e50e843701fc0ff952\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\1136b53f4e42c2fdea34e0348dc06353\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-4\1136b53f4e42c2fdea34e0348dc06353\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f012e423c42ece3ff3fcf50424dbcdf\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f012e423c42ece3ff3fcf50424dbcdf\transformed\grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e64a0f23f578aa698b999b4e57ef44e\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0e64a0f23f578aa698b999b4e57ef44e\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar
ADDED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bd5cfbf87a8079542907b298c855f466\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:12:17-119
uses-permission#android.permission.INTERNET
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f012e423c42ece3ff3fcf50424dbcdf\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-4\3f012e423c42ece3ff3fcf50424dbcdf\transformed\grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c5c8887ad27afe15d16f579db7facb4\transformed\firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8927a844ee1920afe864c3a53174e197\transformed\firebase-firestore-ktx-25.0.0\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\06f73491aee25d029c75af1bff6e8e34\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e95a4dc8fc9a2a597ef4366ce41e3822\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\87b474df0e2285247f4dfe23013de4b1\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\be2279520d6cb9b749def7b903120ca1\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\0f547c843f4a106bc94ad25903c81605\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\89164e8aeb04d1e005737a337c813acc\transformed\play-services-measurement-impl-22.0.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2f216ec89c740e7f554936dad71daf24\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2f216ec89c740e7f554936dad71daf24\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\87ffbadbf57e4c46477bbc9add71123d\transformed\play-services-measurement-sdk-api-22.0.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\e17d963c0ffd5a6ed34f4950fb9101e6\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\10fe1d78162b89254d81f2b842168d7f\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc9b6aea46559204e4a8f724fc603909\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3876bf371af55a7815ccf92395d0175d\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\98440c58c59c602af70fa917c06ed97b\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\e80de4eb89714b804a5dff006ee1a345\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\transforms-4\43e2a0e98f1dce26d5f8bc37ea6e5e5f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bea5b7a692e33546695f4eda025ecd3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5bea5b7a692e33546695f4eda025ecd3\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\112d4d6467d8ba9e634e3c811b14c50d\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\bb690484125a987d5e7794df1b23e3c0\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e237bb86748d298953d7258fba5c001c\transformed\window-1.2.0\AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\9b8a36cac1fb82ceefb6a261d6e2149c\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\5229e2862d6364ca32e3a3bb311a3e6f\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\abd85caff3ff8eef039557ffb8e6d4a5\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\3c3011a6c5bfeae89f93453f662c4113\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
