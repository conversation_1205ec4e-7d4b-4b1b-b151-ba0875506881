{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeReleaseResources-3:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,378,482,574,650,737,826,910,985,1057,1145,1235,1309,1386,1454", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "191,272,373,477,569,645,732,821,905,980,1052,1140,1230,1304,1381,1449,1569"}, "to": {"startLines": "11,12,32,33,34,38,39,96,97,98,99,100,101,102,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1069,1160,3616,3717,3821,4233,4309,10724,10813,10897,10972,11044,11132,11222,11397,11474,11542", "endColumns": "90,80,100,103,91,75,86,88,83,74,71,87,89,73,76,67,119", "endOffsets": "1155,1236,3712,3816,3908,4304,4391,10808,10892,10967,11039,11127,11217,11291,11469,11537,11657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\481238e97b83b818931a2722abe3cf6f\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,88", "endOffsets": "136,225"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11662,11748", "endColumns": "85,88", "endOffsets": "11743,11832"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e262177e497b967ef733f0fbd8d750b5\\transformed\\browser-1.4.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3516,3913,4015,4128", "endColumns": "99,101,112,104", "endOffsets": "3611,4010,4123,4228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6fa2c1a7648815a2389f1e233b858239\\transformed\\material3-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4832,4920,5030,5110,5195,5290,5393,5484,5583,5672,5780,5880,5986,6104,6184,6288", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4827,4915,5025,5105,5190,5285,5388,5479,5578,5667,5775,5875,5981,6099,6179,6283,6378"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4396,4543,4690,4817,4962,5091,5189,5304,5444,5563,5708,5792,5897,5993,6093,6212,6333,6443,6586,6730,6865,7056,7181,7303,7427,7549,7646,7743,7871,8006,8104,8207,8313,8460,8611,8719,8819,8895,8991,9086,9173,9261,9371,9451,9536,9631,9734,9825,9924,10013,10121,10221,10327,10445,10525,10629", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "4538,4685,4812,4957,5086,5184,5299,5439,5558,5703,5787,5892,5988,6088,6207,6328,6438,6581,6725,6860,7051,7176,7298,7422,7544,7641,7738,7866,8001,8099,8202,8308,8455,8606,8714,8814,8890,8986,9081,9168,9256,9366,9446,9531,9626,9729,9820,9919,10008,10116,10216,10322,10440,10520,10624,10719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "4,5,6,7,8,9,10,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "338,436,539,639,742,847,950,11296", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "431,534,634,737,842,945,1064,11392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c08faea6ded0ab196312fa217ce48810\\transformed\\credentials-1.2.0-rc01\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,119", "endOffsets": "163,283"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,218", "endColumns": "112,119", "endOffsets": "213,333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\799b421d1a1acf7bdf6b855ee1ea22a8\\transformed\\play-services-base-18.0.1\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1241,1345,1517,1641,1750,1902,2027,2151,2402,2580,2688,2851,2979,3133,3293,3359,3424", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "1340,1512,1636,1745,1897,2022,2146,2254,2575,2683,2846,2974,3128,3288,3354,3419,3511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc3f110c8971a71c5a78795c57b75cdc\\transformed\\play-services-basement-18.4.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2259", "endColumns": "142", "endOffsets": "2397"}}]}]}