  AuthenticationUnitTest com.dev.aa103_poc  Boolean com.dev.aa103_poc  
SignInUiState com.dev.aa103_poc  String com.dev.aa103_poc  assertEquals com.dev.aa103_poc  assertFalse com.dev.aa103_poc  
assertNull com.dev.aa103_poc  
assertTrue com.dev.aa103_poc  contains com.dev.aa103_poc  forEach com.dev.aa103_poc  indexOf com.dev.aa103_poc  
isNotBlank com.dev.aa103_poc  lastIndexOf com.dev.aa103_poc  listOf com.dev.aa103_poc  Boolean (com.dev.aa103_poc.AuthenticationUnitTest  
SignInUiState (com.dev.aa103_poc.AuthenticationUnitTest  String (com.dev.aa103_poc.AuthenticationUnitTest  Test (com.dev.aa103_poc.AuthenticationUnitTest  assertEquals (com.dev.aa103_poc.AuthenticationUnitTest  assertFalse (com.dev.aa103_poc.AuthenticationUnitTest  
assertNull (com.dev.aa103_poc.AuthenticationUnitTest  
assertTrue (com.dev.aa103_poc.AuthenticationUnitTest  contains (com.dev.aa103_poc.AuthenticationUnitTest  getASSERTEquals (com.dev.aa103_poc.AuthenticationUnitTest  getASSERTFalse (com.dev.aa103_poc.AuthenticationUnitTest  
getASSERTNull (com.dev.aa103_poc.AuthenticationUnitTest  
getASSERTTrue (com.dev.aa103_poc.AuthenticationUnitTest  getAssertEquals (com.dev.aa103_poc.AuthenticationUnitTest  getAssertFalse (com.dev.aa103_poc.AuthenticationUnitTest  
getAssertNull (com.dev.aa103_poc.AuthenticationUnitTest  
getAssertTrue (com.dev.aa103_poc.AuthenticationUnitTest  getCONTAINS (com.dev.aa103_poc.AuthenticationUnitTest  getContains (com.dev.aa103_poc.AuthenticationUnitTest  
getINDEXOf (com.dev.aa103_poc.AuthenticationUnitTest  
getISNotBlank (com.dev.aa103_poc.AuthenticationUnitTest  
getIndexOf (com.dev.aa103_poc.AuthenticationUnitTest  
getIsNotBlank (com.dev.aa103_poc.AuthenticationUnitTest  getLASTIndexOf (com.dev.aa103_poc.AuthenticationUnitTest  	getLISTOf (com.dev.aa103_poc.AuthenticationUnitTest  getLastIndexOf (com.dev.aa103_poc.AuthenticationUnitTest  	getListOf (com.dev.aa103_poc.AuthenticationUnitTest  indexOf (com.dev.aa103_poc.AuthenticationUnitTest  
isNotBlank (com.dev.aa103_poc.AuthenticationUnitTest  isValidEmailFormat (com.dev.aa103_poc.AuthenticationUnitTest  lastIndexOf (com.dev.aa103_poc.AuthenticationUnitTest  listOf (com.dev.aa103_poc.AuthenticationUnitTest  Project com.dev.aa103_poc.data.model  
ProjectStream com.dev.aa103_poc.data.model  invoke .com.dev.aa103_poc.data.model.Project.Companion  ProjectRepositoryInterface !com.dev.aa103_poc.data.repository  stream <com.dev.aa103_poc.data.repository.ProjectRepositoryInterface  AuthGateTest com.dev.aa103_poc.ui.auth  Boolean com.dev.aa103_poc.ui.auth  MockFirebaseUser com.dev.aa103_poc.ui.auth  String com.dev.aa103_poc.ui.auth  assertEquals com.dev.aa103_poc.ui.auth  assertFalse com.dev.aa103_poc.ui.auth  
assertNull com.dev.aa103_poc.ui.auth  
assertTrue com.dev.aa103_poc.ui.auth  
component1 com.dev.aa103_poc.ui.auth  
component2 com.dev.aa103_poc.ui.auth  count com.dev.aa103_poc.ui.auth  forEach com.dev.aa103_poc.ui.auth  indexOf com.dev.aa103_poc.ui.auth  isBlank com.dev.aa103_poc.ui.auth  
isNotBlank com.dev.aa103_poc.ui.auth  lastIndexOf com.dev.aa103_poc.ui.auth  listOf com.dev.aa103_poc.ui.auth  mapOf com.dev.aa103_poc.ui.auth  to com.dev.aa103_poc.ui.auth  trim com.dev.aa103_poc.ui.auth  Boolean &com.dev.aa103_poc.ui.auth.AuthGateTest  MockFirebaseUser &com.dev.aa103_poc.ui.auth.AuthGateTest  String &com.dev.aa103_poc.ui.auth.AuthGateTest  Test &com.dev.aa103_poc.ui.auth.AuthGateTest  assertEquals &com.dev.aa103_poc.ui.auth.AuthGateTest  assertFalse &com.dev.aa103_poc.ui.auth.AuthGateTest  
assertNull &com.dev.aa103_poc.ui.auth.AuthGateTest  
assertTrue &com.dev.aa103_poc.ui.auth.AuthGateTest  
component1 &com.dev.aa103_poc.ui.auth.AuthGateTest  
component2 &com.dev.aa103_poc.ui.auth.AuthGateTest  count &com.dev.aa103_poc.ui.auth.AuthGateTest  getASSERTEquals &com.dev.aa103_poc.ui.auth.AuthGateTest  getASSERTFalse &com.dev.aa103_poc.ui.auth.AuthGateTest  
getASSERTNull &com.dev.aa103_poc.ui.auth.AuthGateTest  
getASSERTTrue &com.dev.aa103_poc.ui.auth.AuthGateTest  getAssertEquals &com.dev.aa103_poc.ui.auth.AuthGateTest  getAssertFalse &com.dev.aa103_poc.ui.auth.AuthGateTest  
getAssertNull &com.dev.aa103_poc.ui.auth.AuthGateTest  
getAssertTrue &com.dev.aa103_poc.ui.auth.AuthGateTest  getCOUNT &com.dev.aa103_poc.ui.auth.AuthGateTest  
getComponent1 &com.dev.aa103_poc.ui.auth.AuthGateTest  
getComponent2 &com.dev.aa103_poc.ui.auth.AuthGateTest  getCount &com.dev.aa103_poc.ui.auth.AuthGateTest  
getINDEXOf &com.dev.aa103_poc.ui.auth.AuthGateTest  
getISBlank &com.dev.aa103_poc.ui.auth.AuthGateTest  
getISNotBlank &com.dev.aa103_poc.ui.auth.AuthGateTest  
getIndexOf &com.dev.aa103_poc.ui.auth.AuthGateTest  
getIsBlank &com.dev.aa103_poc.ui.auth.AuthGateTest  
getIsNotBlank &com.dev.aa103_poc.ui.auth.AuthGateTest  getLASTIndexOf &com.dev.aa103_poc.ui.auth.AuthGateTest  	getLISTOf &com.dev.aa103_poc.ui.auth.AuthGateTest  getLastIndexOf &com.dev.aa103_poc.ui.auth.AuthGateTest  	getListOf &com.dev.aa103_poc.ui.auth.AuthGateTest  getMAPOf &com.dev.aa103_poc.ui.auth.AuthGateTest  getMapOf &com.dev.aa103_poc.ui.auth.AuthGateTest  getTO &com.dev.aa103_poc.ui.auth.AuthGateTest  getTRIM &com.dev.aa103_poc.ui.auth.AuthGateTest  getTo &com.dev.aa103_poc.ui.auth.AuthGateTest  getTrim &com.dev.aa103_poc.ui.auth.AuthGateTest  indexOf &com.dev.aa103_poc.ui.auth.AuthGateTest  isBlank &com.dev.aa103_poc.ui.auth.AuthGateTest  
isNotBlank &com.dev.aa103_poc.ui.auth.AuthGateTest  isValidEmail &com.dev.aa103_poc.ui.auth.AuthGateTest  lastIndexOf &com.dev.aa103_poc.ui.auth.AuthGateTest  listOf &com.dev.aa103_poc.ui.auth.AuthGateTest  mapFirebaseErrorToUserMessage &com.dev.aa103_poc.ui.auth.AuthGateTest  mapOf &com.dev.aa103_poc.ui.auth.AuthGateTest  to &com.dev.aa103_poc.ui.auth.AuthGateTest  trim &com.dev.aa103_poc.ui.auth.AuthGateTest  String 7com.dev.aa103_poc.ui.auth.AuthGateTest.MockFirebaseUser  email 7com.dev.aa103_poc.ui.auth.AuthGateTest.MockFirebaseUser  equals 7com.dev.aa103_poc.ui.auth.AuthGateTest.MockFirebaseUser  Dispatchers com.dev.aa103_poc.ui.projects  ExperimentalCoroutinesApi com.dev.aa103_poc.ui.projects  MockitoAnnotations com.dev.aa103_poc.ui.projects  OptIn com.dev.aa103_poc.ui.projects  Project com.dev.aa103_poc.ui.projects  
ProjectStream com.dev.aa103_poc.ui.projects  ProjectsUiState com.dev.aa103_poc.ui.projects  ProjectsViewModel com.dev.aa103_poc.ui.projects  ProjectsViewModelTest com.dev.aa103_poc.ui.projects  StandardTestDispatcher com.dev.aa103_poc.ui.projects  assertEquals com.dev.aa103_poc.ui.projects  
assertTrue com.dev.aa103_poc.ui.projects  auth com.dev.aa103_poc.ui.projects  	emptyList com.dev.aa103_poc.ui.projects  flowOf com.dev.aa103_poc.ui.projects  listOf com.dev.aa103_poc.ui.projects  
repository com.dev.aa103_poc.ui.projects  	resetMain com.dev.aa103_poc.ui.projects  runTest com.dev.aa103_poc.ui.projects  setMain com.dev.aa103_poc.ui.projects  testDispatcher com.dev.aa103_poc.ui.projects  user com.dev.aa103_poc.ui.projects  whenever com.dev.aa103_poc.ui.projects  Content -com.dev.aa103_poc.ui.projects.ProjectsUiState  Empty -com.dev.aa103_poc.ui.projects.ProjectsUiState  Error -com.dev.aa103_poc.ui.projects.ProjectsUiState  	fromCache -com.dev.aa103_poc.ui.projects.ProjectsUiState  message -com.dev.aa103_poc.ui.projects.ProjectsUiState  projects -com.dev.aa103_poc.ui.projects.ProjectsUiState  syncing -com.dev.aa103_poc.ui.projects.ProjectsUiState  	fromCache 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  projects 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  syncing 5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content  message 3com.dev.aa103_poc.ui.projects.ProjectsUiState.Error  state /com.dev.aa103_poc.ui.projects.ProjectsViewModel  After 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Before 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Dispatchers 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  FirebaseAuth 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  FirebaseUser 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Mock 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  MockitoAnnotations 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Project 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  ProjectRepositoryInterface 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
ProjectStream 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  ProjectsUiState 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  ProjectsViewModel 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  StandardTestDispatcher 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Test 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  assertEquals 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
assertTrue 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  auth 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	emptyList 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  flowOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getASSERTEquals 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getASSERTTrue 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getAssertEquals 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getAssertTrue 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getEMPTYList 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getEmptyList 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getFLOWOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getFlowOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getLISTOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	getListOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getRESETMain 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getRUNTest 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getResetMain 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getRunTest 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getSETMain 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
getSetMain 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getWHENEVER 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  getWhenever 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  invoke 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  listOf 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  
repository 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  	resetMain 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  runTest 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  setMain 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  testDispatcher 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  user 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  whenever 3com.dev.aa103_poc.ui.projects.ProjectsViewModelTest  Boolean com.dev.aa103_poc.ui.signin  
SignInUiState com.dev.aa103_poc.ui.signin  SignInViewModelTest com.dev.aa103_poc.ui.signin  String com.dev.aa103_poc.ui.signin  assertEquals com.dev.aa103_poc.ui.signin  assertFalse com.dev.aa103_poc.ui.signin  
assertNull com.dev.aa103_poc.ui.signin  
assertTrue com.dev.aa103_poc.ui.signin  contains com.dev.aa103_poc.ui.signin  forEach com.dev.aa103_poc.ui.signin  indexOf com.dev.aa103_poc.ui.signin  
isNotBlank com.dev.aa103_poc.ui.signin  lastIndexOf com.dev.aa103_poc.ui.signin  listOf com.dev.aa103_poc.ui.signin  copy )com.dev.aa103_poc.ui.signin.SignInUiState  email )com.dev.aa103_poc.ui.signin.SignInUiState  errorMessage )com.dev.aa103_poc.ui.signin.SignInUiState  	isLoading )com.dev.aa103_poc.ui.signin.SignInUiState  isPasswordVisible )com.dev.aa103_poc.ui.signin.SignInUiState  
isSignedIn )com.dev.aa103_poc.ui.signin.SignInUiState  password )com.dev.aa103_poc.ui.signin.SignInUiState  Boolean /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
SignInUiState /com.dev.aa103_poc.ui.signin.SignInViewModelTest  String /com.dev.aa103_poc.ui.signin.SignInViewModelTest  Test /com.dev.aa103_poc.ui.signin.SignInViewModelTest  assertEquals /com.dev.aa103_poc.ui.signin.SignInViewModelTest  assertFalse /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
assertNull /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
assertTrue /com.dev.aa103_poc.ui.signin.SignInViewModelTest  contains /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getASSERTEquals /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getASSERTFalse /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getASSERTNull /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getASSERTTrue /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getAssertEquals /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getAssertFalse /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getAssertNull /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getAssertTrue /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getCONTAINS /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getContains /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getINDEXOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getISNotBlank /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getIndexOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
getIsNotBlank /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getLASTIndexOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  	getLISTOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  getLastIndexOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  	getListOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  indexOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  
isNotBlank /com.dev.aa103_poc.ui.signin.SignInViewModelTest  isValidEmail /com.dev.aa103_poc.ui.signin.SignInViewModelTest  lastIndexOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  listOf /com.dev.aa103_poc.ui.signin.SignInViewModelTest  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  currentUser %com.google.firebase.auth.FirebaseAuth  getCURRENTUser %com.google.firebase.auth.FirebaseAuth  getCurrentUser %com.google.firebase.auth.FirebaseAuth  setCurrentUser %com.google.firebase.auth.FirebaseAuth  getUID %com.google.firebase.auth.FirebaseUser  getUid %com.google.firebase.auth.FirebaseUser  setUid %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  
AutoCloseable 	java.lang  Dispatchers 	java.lang  ExperimentalCoroutinesApi 	java.lang  MockFirebaseUser 	java.lang  MockitoAnnotations 	java.lang  Project 	java.lang  
ProjectStream 	java.lang  ProjectsViewModel 	java.lang  
SignInUiState 	java.lang  StandardTestDispatcher 	java.lang  assertEquals 	java.lang  assertFalse 	java.lang  
assertNull 	java.lang  
assertTrue 	java.lang  auth 	java.lang  
component1 	java.lang  
component2 	java.lang  contains 	java.lang  count 	java.lang  	emptyList 	java.lang  flowOf 	java.lang  forEach 	java.lang  indexOf 	java.lang  isBlank 	java.lang  
isNotBlank 	java.lang  lastIndexOf 	java.lang  listOf 	java.lang  mapOf 	java.lang  
repository 	java.lang  	resetMain 	java.lang  runTest 	java.lang  setMain 	java.lang  testDispatcher 	java.lang  to 	java.lang  trim 	java.lang  user 	java.lang  whenever 	java.lang  Boolean kotlin  Char kotlin  Dispatchers kotlin  ExperimentalCoroutinesApi kotlin  	Function1 kotlin  Int kotlin  MockFirebaseUser kotlin  MockitoAnnotations kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Project kotlin  
ProjectStream kotlin  ProjectsViewModel kotlin  
SignInUiState kotlin  StandardTestDispatcher kotlin  String kotlin  assertEquals kotlin  assertFalse kotlin  
assertNull kotlin  
assertTrue kotlin  auth kotlin  
component1 kotlin  
component2 kotlin  contains kotlin  count kotlin  	emptyList kotlin  flowOf kotlin  forEach kotlin  indexOf kotlin  isBlank kotlin  
isNotBlank kotlin  lastIndexOf kotlin  listOf kotlin  mapOf kotlin  
repository kotlin  	resetMain kotlin  runTest kotlin  setMain kotlin  testDispatcher kotlin  to kotlin  trim kotlin  user kotlin  whenever kotlin  getCONTAINS 
kotlin.String  getCOUNT 
kotlin.String  getContains 
kotlin.String  getCount 
kotlin.String  
getINDEXOf 
kotlin.String  
getISBlank 
kotlin.String  
getISNotBlank 
kotlin.String  
getIndexOf 
kotlin.String  
getIsBlank 
kotlin.String  
getIsNotBlank 
kotlin.String  getLASTIndexOf 
kotlin.String  getLastIndexOf 
kotlin.String  getTO 
kotlin.String  getTRIM 
kotlin.String  getTo 
kotlin.String  getTrim 
kotlin.String  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  Dispatchers kotlin.annotation  ExperimentalCoroutinesApi kotlin.annotation  MockFirebaseUser kotlin.annotation  MockitoAnnotations kotlin.annotation  Project kotlin.annotation  
ProjectStream kotlin.annotation  ProjectsViewModel kotlin.annotation  
SignInUiState kotlin.annotation  StandardTestDispatcher kotlin.annotation  assertEquals kotlin.annotation  assertFalse kotlin.annotation  
assertNull kotlin.annotation  
assertTrue kotlin.annotation  auth kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  contains kotlin.annotation  count kotlin.annotation  	emptyList kotlin.annotation  flowOf kotlin.annotation  forEach kotlin.annotation  indexOf kotlin.annotation  isBlank kotlin.annotation  
isNotBlank kotlin.annotation  lastIndexOf kotlin.annotation  listOf kotlin.annotation  mapOf kotlin.annotation  
repository kotlin.annotation  	resetMain kotlin.annotation  runTest kotlin.annotation  setMain kotlin.annotation  testDispatcher kotlin.annotation  to kotlin.annotation  trim kotlin.annotation  user kotlin.annotation  whenever kotlin.annotation  Dispatchers kotlin.collections  ExperimentalCoroutinesApi kotlin.collections  List kotlin.collections  Map kotlin.collections  MockFirebaseUser kotlin.collections  MockitoAnnotations kotlin.collections  Project kotlin.collections  
ProjectStream kotlin.collections  ProjectsViewModel kotlin.collections  
SignInUiState kotlin.collections  StandardTestDispatcher kotlin.collections  assertEquals kotlin.collections  assertFalse kotlin.collections  
assertNull kotlin.collections  
assertTrue kotlin.collections  auth kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  count kotlin.collections  	emptyList kotlin.collections  flowOf kotlin.collections  forEach kotlin.collections  indexOf kotlin.collections  isBlank kotlin.collections  
isNotBlank kotlin.collections  lastIndexOf kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  
repository kotlin.collections  	resetMain kotlin.collections  runTest kotlin.collections  setMain kotlin.collections  testDispatcher kotlin.collections  to kotlin.collections  trim kotlin.collections  user kotlin.collections  whenever kotlin.collections  Entry kotlin.collections.Map  
getComponent1 kotlin.collections.Map.Entry  
getComponent2 kotlin.collections.Map.Entry  Dispatchers kotlin.comparisons  ExperimentalCoroutinesApi kotlin.comparisons  MockFirebaseUser kotlin.comparisons  MockitoAnnotations kotlin.comparisons  Project kotlin.comparisons  
ProjectStream kotlin.comparisons  ProjectsViewModel kotlin.comparisons  
SignInUiState kotlin.comparisons  StandardTestDispatcher kotlin.comparisons  assertEquals kotlin.comparisons  assertFalse kotlin.comparisons  
assertNull kotlin.comparisons  
assertTrue kotlin.comparisons  auth kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  contains kotlin.comparisons  count kotlin.comparisons  	emptyList kotlin.comparisons  flowOf kotlin.comparisons  forEach kotlin.comparisons  indexOf kotlin.comparisons  isBlank kotlin.comparisons  
isNotBlank kotlin.comparisons  lastIndexOf kotlin.comparisons  listOf kotlin.comparisons  mapOf kotlin.comparisons  
repository kotlin.comparisons  	resetMain kotlin.comparisons  runTest kotlin.comparisons  setMain kotlin.comparisons  testDispatcher kotlin.comparisons  to kotlin.comparisons  trim kotlin.comparisons  user kotlin.comparisons  whenever kotlin.comparisons  SuspendFunction1 kotlin.coroutines  advanceUntilIdle 1kotlin.coroutines.AbstractCoroutineContextElement  Dispatchers 	kotlin.io  ExperimentalCoroutinesApi 	kotlin.io  MockFirebaseUser 	kotlin.io  MockitoAnnotations 	kotlin.io  Project 	kotlin.io  
ProjectStream 	kotlin.io  ProjectsViewModel 	kotlin.io  
SignInUiState 	kotlin.io  StandardTestDispatcher 	kotlin.io  assertEquals 	kotlin.io  assertFalse 	kotlin.io  
assertNull 	kotlin.io  
assertTrue 	kotlin.io  auth 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  contains 	kotlin.io  count 	kotlin.io  	emptyList 	kotlin.io  flowOf 	kotlin.io  forEach 	kotlin.io  indexOf 	kotlin.io  isBlank 	kotlin.io  
isNotBlank 	kotlin.io  lastIndexOf 	kotlin.io  listOf 	kotlin.io  mapOf 	kotlin.io  
repository 	kotlin.io  	resetMain 	kotlin.io  runTest 	kotlin.io  setMain 	kotlin.io  testDispatcher 	kotlin.io  to 	kotlin.io  trim 	kotlin.io  user 	kotlin.io  whenever 	kotlin.io  Dispatchers 
kotlin.jvm  ExperimentalCoroutinesApi 
kotlin.jvm  MockFirebaseUser 
kotlin.jvm  MockitoAnnotations 
kotlin.jvm  Project 
kotlin.jvm  
ProjectStream 
kotlin.jvm  ProjectsViewModel 
kotlin.jvm  
SignInUiState 
kotlin.jvm  StandardTestDispatcher 
kotlin.jvm  assertEquals 
kotlin.jvm  assertFalse 
kotlin.jvm  
assertNull 
kotlin.jvm  
assertTrue 
kotlin.jvm  auth 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  contains 
kotlin.jvm  count 
kotlin.jvm  	emptyList 
kotlin.jvm  flowOf 
kotlin.jvm  forEach 
kotlin.jvm  indexOf 
kotlin.jvm  isBlank 
kotlin.jvm  
isNotBlank 
kotlin.jvm  lastIndexOf 
kotlin.jvm  listOf 
kotlin.jvm  mapOf 
kotlin.jvm  
repository 
kotlin.jvm  	resetMain 
kotlin.jvm  runTest 
kotlin.jvm  setMain 
kotlin.jvm  testDispatcher 
kotlin.jvm  to 
kotlin.jvm  trim 
kotlin.jvm  user 
kotlin.jvm  whenever 
kotlin.jvm  Dispatchers 
kotlin.ranges  ExperimentalCoroutinesApi 
kotlin.ranges  MockFirebaseUser 
kotlin.ranges  MockitoAnnotations 
kotlin.ranges  Project 
kotlin.ranges  
ProjectStream 
kotlin.ranges  ProjectsViewModel 
kotlin.ranges  
SignInUiState 
kotlin.ranges  StandardTestDispatcher 
kotlin.ranges  assertEquals 
kotlin.ranges  assertFalse 
kotlin.ranges  
assertNull 
kotlin.ranges  
assertTrue 
kotlin.ranges  auth 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  contains 
kotlin.ranges  count 
kotlin.ranges  	emptyList 
kotlin.ranges  flowOf 
kotlin.ranges  forEach 
kotlin.ranges  indexOf 
kotlin.ranges  isBlank 
kotlin.ranges  
isNotBlank 
kotlin.ranges  lastIndexOf 
kotlin.ranges  listOf 
kotlin.ranges  mapOf 
kotlin.ranges  
repository 
kotlin.ranges  	resetMain 
kotlin.ranges  runTest 
kotlin.ranges  setMain 
kotlin.ranges  testDispatcher 
kotlin.ranges  to 
kotlin.ranges  trim 
kotlin.ranges  user 
kotlin.ranges  whenever 
kotlin.ranges  KClass kotlin.reflect  Dispatchers kotlin.sequences  ExperimentalCoroutinesApi kotlin.sequences  MockFirebaseUser kotlin.sequences  MockitoAnnotations kotlin.sequences  Project kotlin.sequences  
ProjectStream kotlin.sequences  ProjectsViewModel kotlin.sequences  
SignInUiState kotlin.sequences  StandardTestDispatcher kotlin.sequences  assertEquals kotlin.sequences  assertFalse kotlin.sequences  
assertNull kotlin.sequences  
assertTrue kotlin.sequences  auth kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  contains kotlin.sequences  count kotlin.sequences  	emptyList kotlin.sequences  flowOf kotlin.sequences  forEach kotlin.sequences  indexOf kotlin.sequences  isBlank kotlin.sequences  
isNotBlank kotlin.sequences  lastIndexOf kotlin.sequences  listOf kotlin.sequences  mapOf kotlin.sequences  
repository kotlin.sequences  	resetMain kotlin.sequences  runTest kotlin.sequences  setMain kotlin.sequences  testDispatcher kotlin.sequences  to kotlin.sequences  trim kotlin.sequences  user kotlin.sequences  whenever kotlin.sequences  assertEquals kotlin.test  
assertTrue kotlin.test  Dispatchers kotlin.text  ExperimentalCoroutinesApi kotlin.text  MockFirebaseUser kotlin.text  MockitoAnnotations kotlin.text  Project kotlin.text  
ProjectStream kotlin.text  ProjectsViewModel kotlin.text  
SignInUiState kotlin.text  StandardTestDispatcher kotlin.text  assertEquals kotlin.text  assertFalse kotlin.text  
assertNull kotlin.text  
assertTrue kotlin.text  auth kotlin.text  
component1 kotlin.text  
component2 kotlin.text  contains kotlin.text  count kotlin.text  	emptyList kotlin.text  flowOf kotlin.text  forEach kotlin.text  indexOf kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  lastIndexOf kotlin.text  listOf kotlin.text  mapOf kotlin.text  
repository kotlin.text  	resetMain kotlin.text  runTest kotlin.text  setMain kotlin.text  testDispatcher kotlin.text  to kotlin.text  trim kotlin.text  user kotlin.text  whenever kotlin.text  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  getRESETMain kotlinx.coroutines.Dispatchers  getResetMain kotlinx.coroutines.Dispatchers  
getSETMain kotlinx.coroutines.Dispatchers  
getSetMain kotlinx.coroutines.Dispatchers  	resetMain kotlinx.coroutines.Dispatchers  setMain kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  value !kotlinx.coroutines.flow.StateFlow  StandardTestDispatcher kotlinx.coroutines.test  TestDispatcher kotlinx.coroutines.test  	TestScope kotlinx.coroutines.test  	resetMain kotlinx.coroutines.test  runTest kotlinx.coroutines.test  setMain kotlinx.coroutines.test  advanceUntilIdle .kotlinx.coroutines.test.TestCoroutineScheduler  	scheduler &kotlinx.coroutines.test.TestDispatcher  Project !kotlinx.coroutines.test.TestScope  
ProjectStream !kotlinx.coroutines.test.TestScope  ProjectsViewModel !kotlinx.coroutines.test.TestScope  assertEquals !kotlinx.coroutines.test.TestScope  
assertTrue !kotlinx.coroutines.test.TestScope  auth !kotlinx.coroutines.test.TestScope  	emptyList !kotlinx.coroutines.test.TestScope  flowOf !kotlinx.coroutines.test.TestScope  getASSERTEquals !kotlinx.coroutines.test.TestScope  
getASSERTTrue !kotlinx.coroutines.test.TestScope  getAUTH !kotlinx.coroutines.test.TestScope  getAssertEquals !kotlinx.coroutines.test.TestScope  
getAssertTrue !kotlinx.coroutines.test.TestScope  getAuth !kotlinx.coroutines.test.TestScope  getEMPTYList !kotlinx.coroutines.test.TestScope  getEmptyList !kotlinx.coroutines.test.TestScope  	getFLOWOf !kotlinx.coroutines.test.TestScope  	getFlowOf !kotlinx.coroutines.test.TestScope  	getLISTOf !kotlinx.coroutines.test.TestScope  	getListOf !kotlinx.coroutines.test.TestScope  
getREPOSITORY !kotlinx.coroutines.test.TestScope  
getRepository !kotlinx.coroutines.test.TestScope  getTESTDispatcher !kotlinx.coroutines.test.TestScope  getTestDispatcher !kotlinx.coroutines.test.TestScope  getUSER !kotlinx.coroutines.test.TestScope  getUser !kotlinx.coroutines.test.TestScope  getWHENEVER !kotlinx.coroutines.test.TestScope  getWhenever !kotlinx.coroutines.test.TestScope  invoke !kotlinx.coroutines.test.TestScope  listOf !kotlinx.coroutines.test.TestScope  
repository !kotlinx.coroutines.test.TestScope  testDispatcher !kotlinx.coroutines.test.TestScope  user !kotlinx.coroutines.test.TestScope  whenever !kotlinx.coroutines.test.TestScope  After 	org.junit  Assert 	org.junit  Before 	org.junit  Test 	org.junit  
SignInUiState org.junit.Assert  assertEquals org.junit.Assert  assertFalse org.junit.Assert  
assertNull org.junit.Assert  
assertTrue org.junit.Assert  contains org.junit.Assert  indexOf org.junit.Assert  
isNotBlank org.junit.Assert  lastIndexOf org.junit.Assert  listOf org.junit.Assert  Mock org.mockito  MockitoAnnotations org.mockito  	openMocks org.mockito.MockitoAnnotations  whenever org.mockito.kotlin  OngoingStubbing org.mockito.stubbing  
thenReturn $org.mockito.stubbing.OngoingStubbing                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                