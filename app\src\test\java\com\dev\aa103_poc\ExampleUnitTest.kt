package com.dev.aa103_poc

import com.dev.aa103_poc.ui.signin.SignInUiState
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for Firebase Authentication integration.
 */
class AuthenticationUnitTest {

    @Test
    fun signInUiState_initialState_isCorrect() {
        val uiState = SignInUiState()

        assertEquals("", uiState.email)
        assertEquals("", uiState.password)
        assertFalse(uiState.isPasswordVisible)
        assertFalse(uiState.isLoading)
        assertNull(uiState.errorMessage)
        assertFalse(uiState.isSignedIn)
    }

    @Test
    fun signInUiState_withEmail_updatesCorrectly() {
        val email = "<EMAIL>"
        val uiState = SignInUiState(email = email)

        assertEquals(email, uiState.email)
    }

    @Test
    fun signInUiState_withPassword_updatesCorrectly() {
        val password = "password123"
        val uiState = SignInUiState(password = password)

        assertEquals(password, uiState.password)
    }

    @Test
    fun signInUiState_togglePasswordVisibility_updatesCorrectly() {
        val uiState = SignInUiState(isPasswordVisible = true)

        assertTrue(uiState.isPasswordVisible)
    }

    @Test
    fun signInUiState_withLoadingState_updatesCorrectly() {
        val uiState = SignInUiState(isLoading = true)

        assertTrue(uiState.isLoading)
    }

    @Test
    fun signInUiState_withErrorMessage_updatesCorrectly() {
        val errorMessage = "Invalid email"
        val uiState = SignInUiState(errorMessage = errorMessage)

        assertEquals(errorMessage, uiState.errorMessage)
    }

    @Test
    fun signInUiState_withSignedInState_updatesCorrectly() {
        val uiState = SignInUiState(isSignedIn = true)

        assertTrue(uiState.isSignedIn)
    }

    @Test
    fun emailValidation_validEmail_returnsTrue() {
        val validEmails = listOf(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        )

        validEmails.forEach { email ->
            assertTrue("$email should be valid", isValidEmailFormat(email))
        }
    }

    @Test
    fun emailValidation_invalidEmail_returnsFalse() {
        val invalidEmails = listOf(
            "invalid-email",
            "@example.com",
            "test@",
            "test.example.com",
            ""
        )

        invalidEmails.forEach { email ->
            assertFalse("$email should be invalid", isValidEmailFormat(email))
        }
    }

    // Helper function to test email validation logic
    private fun isValidEmailFormat(email: String): Boolean {
        // Simple email validation for testing (avoiding Android dependencies)
        return email.isNotBlank() &&
               email.contains("@") &&
               email.contains(".") &&
               email.indexOf("@") > 0 &&
               email.lastIndexOf(".") > email.indexOf("@")
    }
}