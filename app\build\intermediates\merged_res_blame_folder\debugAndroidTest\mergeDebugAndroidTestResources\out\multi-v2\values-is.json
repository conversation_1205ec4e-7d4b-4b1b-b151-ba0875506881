{"logs": [{"outputFile": "com.dev.aa103_poc.test.app-mergeDebugAndroidTestResources-1:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,307,404,504,607,711,1990", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "195,302,399,499,602,706,817,2086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,974,1042,1120,1203,1273,1350,1418", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,969,1037,1115,1198,1268,1345,1413,1533"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "822,913,994,1093,1192,1277,1357,1452,1541,1623,1691,1759,1837,1920,2091,2168,2236", "endColumns": "90,80,98,98,84,79,94,88,81,67,67,77,82,69,76,67,119", "endOffsets": "908,989,1088,1187,1272,1352,1447,1536,1618,1686,1754,1832,1915,1985,2163,2231,2351"}}]}]}