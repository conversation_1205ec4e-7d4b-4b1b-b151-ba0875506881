{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-3:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6fa2c1a7648815a2389f1e233b858239\\transformed\\material3-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,347,469,617,743,837,949,1091,1210,1369,1453,1554,1655,1756,1877,2012,2118,2268,2414,2550,2752,2881,2999,3122,3255,3357,3462,3586,3714,3816,3928,4033,4178,4330,4439,4548,4626,4719,4814,4904,4990,5097,5177,5262,5359,5470,5563,5667,5755,5871,5972,6081,6203,6283,6393", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "197,342,464,612,738,832,944,1086,1205,1364,1448,1549,1650,1751,1872,2007,2113,2263,2409,2545,2747,2876,2994,3117,3250,3352,3457,3581,3709,3811,3923,4028,4173,4325,4434,4543,4621,4714,4809,4899,4985,5092,5172,5257,5354,5465,5558,5662,5750,5866,5967,6076,6198,6278,6388,6485"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4560,4707,4852,4974,5122,5248,5342,5454,5596,5715,5874,5958,6059,6160,6261,6382,6517,6623,6773,6919,7055,7257,7386,7504,7627,7760,7862,7967,8091,8219,8321,8433,8538,8683,8835,8944,9053,9131,9224,9319,9409,9495,9602,9682,9767,9864,9975,10068,10172,10260,10376,10477,10586,10708,10788,10898", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "4702,4847,4969,5117,5243,5337,5449,5591,5710,5869,5953,6054,6155,6256,6377,6512,6618,6768,6914,7050,7252,7381,7499,7622,7755,7857,7962,8086,8214,8316,8428,8533,8678,8830,8939,9048,9126,9219,9314,9404,9490,9597,9677,9762,9859,9970,10063,10167,10255,10371,10472,10581,10703,10783,10893,10990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\481238e97b83b818931a2722abe3cf6f\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11926,12014", "endColumns": "87,94", "endOffsets": "12009,12104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c08faea6ded0ab196312fa217ce48810\\transformed\\credentials-1.2.0-rc01\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,171", "endColumns": "115,118", "endOffsets": "166,285"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,221", "endColumns": "115,118", "endOffsets": "216,335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "11,12,32,33,34,38,39,96,97,98,99,100,101,102,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1063,1162,3769,3867,3973,4386,4466,10995,11087,11174,11245,11313,11394,11479,11656,11735,11804", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "1157,1245,3862,3968,4055,4461,4555,11082,11169,11240,11308,11389,11474,11550,11730,11799,11921"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\799b421d1a1acf7bdf6b855ee1ea22a8\\transformed\\play-services-base-18.0.1\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2300", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,59,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2299,2379"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1250,1356,1536,1666,1775,1946,2079,2200,2474,2669,2781,2966,3102,3262,3441,3514,3578", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,63,83", "endOffsets": "1351,1531,1661,1770,1941,2074,2195,2308,2664,2776,2961,3097,3257,3436,3509,3573,3657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc3f110c8971a71c5a78795c57b75cdc\\transformed\\play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2313", "endColumns": "160", "endOffsets": "2469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "4,5,6,7,8,9,10,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "340,438,540,639,741,845,949,11555", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "433,535,634,736,840,944,1058,11651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e262177e497b967ef733f0fbd8d750b5\\transformed\\browser-1.4.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3662,4060,4162,4281", "endColumns": "106,101,118,104", "endOffsets": "3764,4157,4276,4381"}}]}]}