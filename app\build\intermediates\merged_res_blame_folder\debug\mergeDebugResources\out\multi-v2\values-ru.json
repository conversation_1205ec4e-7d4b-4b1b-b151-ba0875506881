{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeDebugResources-3:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e262177e497b967ef733f0fbd8d750b5\\transformed\\browser-1.4.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3510,3910,4015,4127", "endColumns": "107,104,111,104", "endOffsets": "3613,4010,4122,4227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\481238e97b83b818931a2722abe3cf6f\\transformed\\foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11471,11559", "endColumns": "87,90", "endOffsets": "11554,11645"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c08faea6ded0ab196312fa217ce48810\\transformed\\credentials-1.2.0-rc01\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,215", "endColumns": "109,118", "endOffsets": "210,329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "4,5,6,7,8,9,10,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "334,432,534,635,736,841,944,11093", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "427,529,630,731,836,939,1056,11189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\799b421d1a1acf7bdf6b855ee1ea22a8\\transformed\\play-services-base-18.0.1\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1237,1344,1510,1636,1746,1888,2017,2132,2393,2574,2681,2844,2970,3137,3295,3364,3424", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "1339,1505,1631,1741,1883,2012,2127,2231,2569,2676,2839,2965,3132,3290,3359,3419,3505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc3f110c8971a71c5a78795c57b75cdc\\transformed\\play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2236", "endColumns": "156", "endOffsets": "2388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6fa2c1a7648815a2389f1e233b858239\\transformed\\material3-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,721,835,976,1093,1233,1317,1415,1508,1606,1721,1844,1947,2076,2204,2330,2510,2634,2757,2884,3004,3098,3198,3319,3452,3550,3664,3771,3903,4041,4151,4251,4336,4431,4527,4621,4708,4816,4896,4980,5078,5179,5273,5368,5456,5563,5661,5760,5907,5987,6093", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "168,284,402,520,619,716,830,971,1088,1228,1312,1410,1503,1601,1716,1839,1942,2071,2199,2325,2505,2629,2752,2879,2999,3093,3193,3314,3447,3545,3659,3766,3898,4036,4146,4246,4331,4426,4522,4616,4703,4811,4891,4975,5073,5174,5268,5363,5451,5558,5656,5755,5902,5982,6088,6185"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4404,4522,4638,4756,4874,4973,5070,5184,5325,5442,5582,5666,5764,5857,5955,6070,6193,6296,6425,6553,6679,6859,6983,7106,7233,7353,7447,7547,7668,7801,7899,8013,8120,8252,8390,8500,8600,8685,8780,8876,8970,9057,9165,9245,9329,9427,9528,9622,9717,9805,9912,10010,10109,10256,10336,10442", "endColumns": "117,115,117,117,98,96,113,140,116,139,83,97,92,97,114,122,102,128,127,125,179,123,122,126,119,93,99,120,132,97,113,106,131,137,109,99,84,94,95,93,86,107,79,83,97,100,93,94,87,106,97,98,146,79,105,96", "endOffsets": "4517,4633,4751,4869,4968,5065,5179,5320,5437,5577,5661,5759,5852,5950,6065,6188,6291,6420,6548,6674,6854,6978,7101,7228,7348,7442,7542,7663,7796,7894,8008,8115,8247,8385,8495,8595,8680,8775,8871,8965,9052,9160,9240,9324,9422,9523,9617,9712,9800,9907,10005,10104,10251,10331,10437,10534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "11,12,32,33,34,38,39,96,97,98,99,100,101,102,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1061,1154,3618,3716,3818,4232,4314,10539,10627,10709,10780,10850,10934,11021,11194,11278,11348", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "1149,1232,3711,3813,3905,4309,4399,10622,10704,10775,10845,10929,11016,11088,11273,11343,11466"}}]}]}