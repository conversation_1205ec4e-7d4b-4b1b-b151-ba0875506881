1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dev.aa103_poc"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
11-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
12-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:22-76
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:5-68
13-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:25:22-65
14    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
14-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:5-110
14-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:26:22-107
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:5-79
15-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:25:22-76
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
16-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:5-88
16-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:26:22-85
17    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
17-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:5-82
17-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:27:22-79
18    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
18-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\24a49126bb18c9b96a314e1c8686f42e\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
18-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\24a49126bb18c9b96a314e1c8686f42e\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
19
20    <permission
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
21        android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.dev.aa103_poc.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
25
26    <application
26-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:5:5-31:19
27        android:allowBackup="true"
27-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:6:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\8e2933c94d70716d0d033357ed075e2d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
29        android:dataExtractionRules="@xml/data_extraction_rules"
29-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:7:9-65
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:8:9-54
32        android:icon="@mipmap/ic_launcher"
32-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:9:9-43
33        android:label="@string/app_name"
33-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:10:9-41
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:11:9-54
35        android:supportsRtl="true"
35-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:12:9-35
36        android:theme="@style/Theme.AA103_POC" >
36-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:13:9-47
37        <activity
37-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:15:9-25:20
38            android:name="com.dev.aa103_poc.MainActivity"
38-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:16:13-41
39            android:exported="true"
39-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:17:13-36
40            android:label="@string/app_name"
40-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:18:13-45
41            android:theme="@style/Theme.AA103_POC" >
41-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:19:13-51
42            <intent-filter>
42-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:20:13-24:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:17-69
43-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:21:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:17-77
45-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:23:27-74
46            </intent-filter>
47        </activity>
48        <activity
48-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:26:9-30:54
49            android:name="com.dev.aa103_poc.ui.signin.SignInActivity"
49-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:27:13-53
50            android:exported="false"
50-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:28:13-37
51            android:label="Sign In"
51-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:29:13-36
52            android:theme="@style/Theme.AA103_POC" />
52-->C:\Users\<USER>\Github\AA103_POC\app\src\main\AndroidManifest.xml:30:13-51
53
54        <service
54-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:8:9-14:19
55            android:name="com.google.firebase.components.ComponentDiscoveryService"
55-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:9:13-84
56            android:directBootAware="true"
56-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
57            android:exported="false" >
57-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:10:13-37
58            <meta-data
58-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:11:13-13:85
59                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
59-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:12:17-119
60                android:value="com.google.firebase.components.ComponentRegistrar" />
60-->[com.google.firebase:firebase-auth-ktx:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\51e959a861d337b2b446d859bc9d07e3\transformed\firebase-auth-ktx-23.0.0\AndroidManifest.xml:13:17-82
61            <meta-data
61-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
62                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
62-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
63                android:value="com.google.firebase.components.ComponentRegistrar" />
63-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
64            <meta-data
64-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:37:13-39:85
65                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
65-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:38:17-139
66                android:value="com.google.firebase.components.ComponentRegistrar" />
66-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:39:17-82
67            <meta-data
67-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
68                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
68-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
69                android:value="com.google.firebase.components.ComponentRegistrar" />
69-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
70            <meta-data
70-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
71                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
71-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
72                android:value="com.google.firebase.components.ComponentRegistrar" />
72-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3aada4af01b9393c8729d71d01331206\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
73            <meta-data
73-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\793200600de3b11375042a8058e75108\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
74                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
74-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\793200600de3b11375042a8058e75108\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
75                android:value="com.google.firebase.components.ComponentRegistrar" />
75-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\793200600de3b11375042a8058e75108\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
76            <meta-data
76-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
77                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
77-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
78                android:value="com.google.firebase.components.ComponentRegistrar" />
78-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
79        </service>
80
81        <activity
81-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
82            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
82-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
83            android:excludeFromRecents="true"
83-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
84            android:exported="true"
84-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
85            android:launchMode="singleTask"
85-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
86            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
86-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
87            <intent-filter>
87-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
88                <action android:name="android.intent.action.VIEW" />
88-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
88-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
89
90                <category android:name="android.intent.category.DEFAULT" />
90-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
90-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
91                <category android:name="android.intent.category.BROWSABLE" />
91-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
91-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
92
93                <data
93-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
94                    android:host="firebase.auth"
94-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
95                    android:path="/"
95-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
96                    android:scheme="genericidp" />
96-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
97            </intent-filter>
98        </activity>
99        <activity
99-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
100            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
100-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
101            android:excludeFromRecents="true"
101-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
102            android:exported="true"
102-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
103            android:launchMode="singleTask"
103-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
104            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
105            <intent-filter>
105-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
106                <action android:name="android.intent.action.VIEW" />
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
107
108                <category android:name="android.intent.category.DEFAULT" />
108-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
108-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
109                <category android:name="android.intent.category.BROWSABLE" />
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
110
111                <data
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
112                    android:host="firebase.auth"
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
113                    android:path="/"
113-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
114                    android:scheme="recaptcha" />
114-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d98cea3ecb5bec86fc2aaa651791db8\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
115            </intent-filter>
116        </activity>
117
118        <receiver
118-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:29:9-33:20
119            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
119-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:30:13-85
120            android:enabled="true"
120-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:31:13-35
121            android:exported="false" >
121-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:32:13-37
122        </receiver>
123
124        <service
124-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:35:9-38:40
125            android:name="com.google.android.gms.measurement.AppMeasurementService"
125-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:36:13-84
126            android:enabled="true"
126-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:37:13-35
127            android:exported="false" />
127-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:38:13-37
128        <service
128-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:39:9-43:72
129            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
129-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:40:13-87
130            android:enabled="true"
130-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:41:13-35
131            android:exported="false"
131-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:42:13-37
132            android:permission="android.permission.BIND_JOB_SERVICE" />
132-->[com.google.android.gms:play-services-measurement:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\2d628625e29d253fd7ca487f44fea4aa\transformed\play-services-measurement-22.0.2\AndroidManifest.xml:43:13-69
133
134        <property
134-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:30:9-32:61
135            android:name="android.adservices.AD_SERVICES_CONFIG"
135-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:31:13-65
136            android:resource="@xml/ga_ad_services_config" />
136-->[com.google.android.gms:play-services-measurement-api:22.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\826921fea0e8a8b344566e9dad3f31bb\transformed\play-services-measurement-api-22.0.2\AndroidManifest.xml:32:13-58
137
138        <service
138-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
139            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
139-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
140            android:enabled="true"
140-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
141            android:exported="false" >
141-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
142            <meta-data
142-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
143                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
143-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
144                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
144-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
145        </service>
146
147        <activity
147-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
148            android:name="androidx.credentials.playservices.HiddenActivity"
148-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
149            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
149-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
150            android:enabled="true"
150-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
151            android:exported="false"
151-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
152            android:fitsSystemWindows="true"
152-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
153            android:theme="@style/Theme.Hidden" >
153-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-4\e2d099b519fdc69439ea38afa004a592\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
154        </activity>
155        <activity
155-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
156            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
156-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
157            android:excludeFromRecents="true"
157-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
158            android:exported="false"
158-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
159            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
159-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
160        <!--
161            Service handling Google Sign-In user revocation. For apps that do not integrate with
162            Google Sign-In, this service will never be started.
163        -->
164        <service
164-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
165            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
165-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
166            android:exported="true"
166-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
167            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
167-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
168            android:visibleToInstantApps="true" />
168-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f851839c5eed7487dd3437b607c2f07\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
169
170        <provider
170-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
171            android:name="com.google.firebase.provider.FirebaseInitProvider"
171-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
172            android:authorities="com.dev.aa103_poc.firebaseinitprovider"
172-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
173            android:directBootAware="true"
173-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
174            android:exported="false"
174-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
175            android:initOrder="100" />
175-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa834382b9d69e98c7a6ecb8bbcda1aa\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
176
177        <activity
177-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
178            android:name="com.google.android.gms.common.api.GoogleApiActivity"
178-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
179            android:exported="false"
179-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
180            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
180-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\799b421d1a1acf7bdf6b855ee1ea22a8\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
181
182        <provider
182-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
183            android:name="androidx.startup.InitializationProvider"
183-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
184            android:authorities="com.dev.aa103_poc.androidx-startup"
184-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
185            android:exported="false" >
185-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
186            <meta-data
186-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
187                android:name="androidx.emoji2.text.EmojiCompatInitializer"
187-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
188                android:value="androidx.startup" />
188-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c84adfefa0e7593c3ba9b91fc6f89e6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
189            <meta-data
189-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\86b55738124e10db4ab5aca31d5c7001\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
190                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
190-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\86b55738124e10db4ab5aca31d5c7001\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
191                android:value="androidx.startup" />
191-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\transforms-4\86b55738124e10db4ab5aca31d5c7001\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
192            <meta-data
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
193                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
194                android:value="androidx.startup" />
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
195        </provider>
196
197        <uses-library
197-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\7b16836c0fecc34e938dff4e2a236ac3\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
198            android:name="android.ext.adservices"
198-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\7b16836c0fecc34e938dff4e2a236ac3\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
199            android:required="false" />
199-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\7b16836c0fecc34e938dff4e2a236ac3\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
200
201        <meta-data
201-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc3f110c8971a71c5a78795c57b75cdc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
202            android:name="com.google.android.gms.version"
202-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc3f110c8971a71c5a78795c57b75cdc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
203            android:value="@integer/google_play_services_version" />
203-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc3f110c8971a71c5a78795c57b75cdc\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
204
205        <receiver
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
206            android:name="androidx.profileinstaller.ProfileInstallReceiver"
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
207            android:directBootAware="false"
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
208            android:enabled="true"
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
209            android:exported="true"
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
210            android:permission="android.permission.DUMP" >
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
212                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
215                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
218                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
219            </intent-filter>
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
221                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\69c46d52ab3c3bfb2dcf3bba35f44709\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
222            </intent-filter>
223        </receiver>
224    </application>
225
226</manifest>
