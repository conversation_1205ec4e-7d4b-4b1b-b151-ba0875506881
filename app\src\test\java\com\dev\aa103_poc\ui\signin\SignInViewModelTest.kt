package com.dev.aa103_poc.ui.signin

import org.junit.Test
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.Assert.assertTrue

/**
 * Unit tests for SignInUiState data class and related business logic.
 *
 * Note: These tests focus on the data structures and validation logic
 * without Firebase dependencies to avoid initialization issues in unit tests.
 */
class SignInViewModelTest {

    @Test
    fun `SignInUiState should have correct default values`() {
        val uiState = SignInUiState()

        assertEquals("", uiState.email)
        assertEquals("", uiState.password)
        assertFalse(uiState.isPasswordVisible)
        assertFalse(uiState.isLoading)
        assertNull(uiState.errorMessage)
        assertFalse(uiState.isSignedIn)
    }

    @Test
    fun `SignInUiState copy should work correctly`() {
        val originalState = SignInUiState()
        val newEmail = "<EMAIL>"

        val updatedState = originalState.copy(email = newEmail)

        assertEquals(newEmail, updatedState.email)
        assertEquals(originalState.password, updatedState.password)
        assertEquals(originalState.isPasswordVisible, updatedState.isPasswordVisible)
        assertEquals(originalState.isLoading, updatedState.isLoading)
        assertEquals(originalState.errorMessage, updatedState.errorMessage)
        assertEquals(originalState.isSignedIn, updatedState.isSignedIn)
    }

    @Test
    fun `SignInUiState with loading state should be correct`() {
        val loadingState = SignInUiState(isLoading = true)

        assertTrue(loadingState.isLoading)
        assertFalse(loadingState.isSignedIn)
    }

    @Test
    fun `SignInUiState with error message should be correct`() {
        val errorMessage = "Invalid email"
        val errorState = SignInUiState(errorMessage = errorMessage)

        assertEquals(errorMessage, errorState.errorMessage)
        assertFalse(errorState.isLoading)
        assertFalse(errorState.isSignedIn)
    }

    @Test
    fun `SignInUiState with signed in state should be correct`() {
        val signedInState = SignInUiState(isSignedIn = true)

        assertTrue(signedInState.isSignedIn)
        assertFalse(signedInState.isLoading)
        assertNull(signedInState.errorMessage)
    }

    @Test
    fun `SignInUiState with password visibility should be correct`() {
        val visiblePasswordState = SignInUiState(isPasswordVisible = true)

        assertTrue(visiblePasswordState.isPasswordVisible)

        val hiddenPasswordState = SignInUiState(isPasswordVisible = false)
        assertFalse(hiddenPasswordState.isPasswordVisible)
    }

    @Test
    fun `email validation logic should work correctly`() {
        // Test valid emails
        val validEmails = listOf(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        )

        validEmails.forEach { email ->
            assertTrue("$email should be valid", isValidEmail(email))
        }

        // Test invalid emails
        val invalidEmails = listOf(
            "",
            "invalid-email",
            "@example.com",
            "test@",
            "test.example.com"
        )

        invalidEmails.forEach { email ->
            assertFalse("$email should be invalid", isValidEmail(email))
        }
    }

    @Test
    fun `password validation logic should work correctly`() {
        assertTrue("Non-empty password should be valid", "password123".isNotBlank())
        assertFalse("Empty password should be invalid", "".isNotBlank())
        assertFalse("Blank password should be invalid", "   ".isNotBlank())
    }

    // Helper function for email validation (simplified for testing)
    private fun isValidEmail(email: String): Boolean {
        return email.isNotBlank() &&
               email.contains("@") &&
               email.contains(".") &&
               email.indexOf("@") > 0 &&
               email.lastIndexOf(".") > email.indexOf("@")
    }
}
