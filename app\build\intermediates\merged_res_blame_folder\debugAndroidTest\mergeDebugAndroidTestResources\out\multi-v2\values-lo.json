{"logs": [{"outputFile": "com.dev.aa103_poc.test.app-mergeDebugAndroidTestResources-1:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\abd85caff3ff8eef039557ffb8e6d4a5\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,201,304,403,501,602,700,1991", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "196,299,398,496,597,695,806,2087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\48127b829acf9e3a2697ec3974b843b9\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "811,901,978,1087,1185,1274,1363,1453,1539,1622,1687,1753,1833,1917,2092,2170,2236", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "896,973,1082,1180,1269,1358,1448,1534,1617,1682,1748,1828,1912,1986,2165,2231,2352"}}]}]}