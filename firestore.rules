rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document and subcollections
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Projects subcollection - users can only access their own projects
      match /projects/{projectId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
        allow create: if request.auth != null 
                      && request.auth.uid == userId
                      && request.resource.data.ownerUid == request.auth.uid;
      }
    }
  }
}