package com.dev.aa103_poc.data.repository

import com.dev.aa103_poc.data.model.Project
import com.dev.aa103_poc.data.model.ProjectStream
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.Source
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firestore implementation of ProjectRepositoryInterface
 * Provides manual loading and refreshing of projects with offline support
 */
@Singleton
class FirestoreProjectRepository @Inject constructor(
    private val db: FirebaseFirestore
) : ProjectRepositoryInterface {

    override suspend fun loadFromCache(uid: String): ProjectStream {
        val ref = getProjectsRef(uid)

        return try {
            val snapshot = ref.get(Source.CACHE).await()
            val items = snapshot.documents.map { document ->
                mapDocumentToProject(document)
            }
            ProjectStream(
                items = items,
                fromCache = true,
                hasPendingWrites = snapshot.metadata.hasPendingWrites()
            )
        } catch (e: Exception) {
            // Cache might be empty
            ProjectStream(
                items = emptyList(),
                fromCache = true,
                hasPendingWrites = false
            )
        }
    }

    override suspend fun refreshFromServer(uid: String): ProjectStream {
        val ref = getProjectsRef(uid)

        val snapshot = ref.get(Source.SERVER).await()
        val items = snapshot.documents.map { document ->
            mapDocumentToProject(document)
        }

        return ProjectStream(
            items = items,
            fromCache = false,
            hasPendingWrites = snapshot.metadata.hasPendingWrites()
        )
    }

    private fun getProjectsRef(uid: String) = db.collection("users")
        .document(uid)
        .collection("projects")
        .orderBy("updatedAt", Query.Direction.DESCENDING)

    private fun mapDocumentToProject(document: com.google.firebase.firestore.DocumentSnapshot): Project {
        return Project(
            id = document.getString("id") ?: document.id,
            title = document.getString("title") ?: "",
            type = document.getString("type") ?: "",
            jobType = document.getString("jobType") ?: "",
            ownerUid = document.getString("ownerUid") ?: "",
            createdAt = document.getTimestamp("createdAt"),
            updatedAt = document.getTimestamp("updatedAt")
        )
    }
}
