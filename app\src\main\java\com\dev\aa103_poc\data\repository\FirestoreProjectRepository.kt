package com.dev.aa103_poc.data.repository

import com.dev.aa103_poc.data.model.Project
import com.dev.aa103_poc.data.model.ProjectStream
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.Source
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firestore implementation of ProjectRepositoryInterface
 * Provides real-time streaming of projects with offline support
 */
@Singleton
class FirestoreProjectRepository @Inject constructor(
    private val db: FirebaseFirestore
) : ProjectRepositoryInterface {

    override fun stream(uid: String): Flow<ProjectStream> = callbackFlow {
        val ref = db.collection("users")
            .document(uid)
            .collection("projects")
            .orderBy("updatedAt", Query.Direction.DESCENDING)

        // First, load from cache
        try {
            val cacheSnapshot = ref.get(Source.CACHE).await()
            val cacheItems = cacheSnapshot.documents.map { document ->
                mapDocumentToProject(document)
            }
            trySend(
                ProjectStream(
                    items = cacheItems,
                    fromCache = true,
                    hasPendingWrites = cacheSnapshot.metadata.hasPendingWrites()
                )
            )
        } catch (e: Exception) {
            // Cache might be empty, continue to server
        }

        // Then, load from server and set up real-time listener
        val registration = ref.addSnapshotListener { querySnapshot, error ->
            if (error != null) {
                close(error)
                return@addSnapshotListener
            }

            if (querySnapshot == null) return@addSnapshotListener

            val items = querySnapshot.documents.map { document ->
                mapDocumentToProject(document)
            }

            trySend(
                ProjectStream(
                    items = items,
                    fromCache = querySnapshot.metadata.isFromCache,
                    hasPendingWrites = querySnapshot.metadata.hasPendingWrites()
                )
            )
        }

        awaitClose { registration.remove() }
    }

    private fun mapDocumentToProject(document: com.google.firebase.firestore.DocumentSnapshot): Project {
        return Project(
            id = document.getString("id") ?: document.id,
            title = document.getString("title") ?: "",
            type = document.getString("type") ?: "",
            jobType = document.getString("jobType") ?: "",
            ownerUid = document.getString("ownerUid") ?: "",
            createdAt = document.getTimestamp("createdAt"),
            updatedAt = document.getTimestamp("updatedAt")
        )
    }
}
