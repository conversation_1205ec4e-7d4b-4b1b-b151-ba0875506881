"com.dev.aa103_poc.AA103Applicationcom.dev.aa103_poc.MainActivity$com.dev.aa103_poc.data.model.Project.com.dev.aa103_poc.data.model.Project.Companion*com.dev.aa103_poc.data.model.ProjectStream<com.dev.aa103_poc.data.repository.FirestoreProjectRepository3com.dev.aa103_poc.data.repository.ProjectRepository<com.dev.aa103_poc.data.repository.ProjectRepositoryInterfacecom.dev.aa103_poc.di.AppModule%com.dev.aa103_poc.di.RepositoryModule8com.dev.aa103_poc.ui.createproject.CreateProjectActivity7com.dev.aa103_poc.ui.createproject.CreateProjectUiState9com.dev.aa103_poc.ui.createproject.CreateProjectViewModelCcom.dev.aa103_poc.ui.createproject.CreateProjectViewModel.Companion-com.dev.aa103_poc.ui.projects.ProjectsUiState5com.dev.aa103_poc.ui.projects.ProjectsUiState.Loading5com.dev.aa103_poc.ui.projects.ProjectsUiState.Content3com.dev.aa103_poc.ui.projects.ProjectsUiState.Empty3com.dev.aa103_poc.ui.projects.ProjectsUiState.Error/com.dev.aa103_poc.ui.projects.ProjectsViewModel*com.dev.aa103_poc.ui.signin.SignInActivity)com.dev.aa103_poc.ui.signin.SignInUiState+com.dev.aa103_poc.ui.signin.SignInViewModel5com.dev.aa103_poc.ui.signin.SignInViewModel.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    