package com.dev.aa103_poc.data.repository;

/**
 * Interface for project repository operations
 * Defines contract for loading project data with manual refresh
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006\b"}, d2 = {"Lcom/dev/aa103_poc/data/repository/ProjectRepositoryInterface;", "", "loadFromCache", "Lcom/dev/aa103_poc/data/model/ProjectStream;", "uid", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshFromServer", "app_debug"})
public abstract interface ProjectRepositoryInterface {
    
    /**
     * Load projects for a specific user from cache
     * @param uid The user ID to load projects for
     * @return ProjectStream with cached data
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object loadFromCache(@org.jetbrains.annotations.NotNull()
    java.lang.String uid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.dev.aa103_poc.data.model.ProjectStream> $completion);
    
    /**
     * Refresh projects for a specific user from server
     * @param uid The user ID to refresh projects for
     * @return ProjectStream with fresh server data
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object refreshFromServer(@org.jetbrains.annotations.NotNull()
    java.lang.String uid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.dev.aa103_poc.data.model.ProjectStream> $completion);
}