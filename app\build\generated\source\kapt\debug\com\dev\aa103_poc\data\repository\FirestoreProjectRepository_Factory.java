package com.dev.aa103_poc.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FirestoreProjectRepository_Factory implements Factory<FirestoreProjectRepository> {
  private final Provider<FirebaseFirestore> dbProvider;

  public FirestoreProjectRepository_Factory(Provider<FirebaseFirestore> dbProvider) {
    this.dbProvider = dbProvider;
  }

  @Override
  public FirestoreProjectRepository get() {
    return newInstance(dbProvider.get());
  }

  public static FirestoreProjectRepository_Factory create(Provider<FirebaseFirestore> dbProvider) {
    return new FirestoreProjectRepository_Factory(dbProvider);
  }

  public static FirestoreProjectRepository newInstance(FirebaseFirestore db) {
    return new FirestoreProjectRepository(db);
  }
}
