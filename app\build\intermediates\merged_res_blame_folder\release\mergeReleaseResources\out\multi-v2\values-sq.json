{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeReleaseResources-3:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1005,1072,1159,1250,1323,1400,1466", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1000,1067,1154,1245,1318,1395,1461,1582"}, "to": {"startLines": "11,12,32,33,34,38,39,96,97,98,99,100,101,102,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1080,1175,3714,3813,3915,4324,4405,10714,10806,10896,10965,11032,11119,11210,11384,11461,11527", "endColumns": "94,82,98,101,95,80,92,91,89,68,66,86,90,72,76,65,120", "endOffsets": "1170,1253,3808,3910,4006,4400,4493,10801,10891,10960,11027,11114,11205,11278,11456,11522,11643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc3f110c8971a71c5a78795c57b75cdc\\transformed\\play-services-basement-18.4.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2319", "endColumns": "128", "endOffsets": "2443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\799b421d1a1acf7bdf6b855ee1ea22a8\\transformed\\play-services-base-18.0.1\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1258,1365,1538,1675,1782,1943,2077,2203,2448,2618,2726,2901,3039,3201,3385,3450,3517", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "1360,1533,1670,1777,1938,2072,2198,2314,2613,2721,2896,3034,3196,3380,3445,3512,3594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6fa2c1a7648815a2389f1e233b858239\\transformed\\material3-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,409,527,629,724,836,974,1090,1236,1320,1420,1512,1611,1729,1853,1958,2095,2229,2373,2562,2700,2823,2947,3073,3166,3262,3387,3528,3623,3734,3843,3982,4127,4238,4337,4414,4508,4602,4690,4773,4878,4964,5047,5146,5247,5342,5440,5528,5634,5734,5837,5965,6050,6164", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "169,285,404,522,624,719,831,969,1085,1231,1315,1415,1507,1606,1724,1848,1953,2090,2224,2368,2557,2695,2818,2942,3068,3161,3257,3382,3523,3618,3729,3838,3977,4122,4233,4332,4409,4503,4597,4685,4768,4873,4959,5042,5141,5242,5337,5435,5523,5629,5729,5832,5960,6045,6159,6266"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4498,4617,4733,4852,4970,5072,5167,5279,5417,5533,5679,5763,5863,5955,6054,6172,6296,6401,6538,6672,6816,7005,7143,7266,7390,7516,7609,7705,7830,7971,8066,8177,8286,8425,8570,8681,8780,8857,8951,9045,9133,9216,9321,9407,9490,9589,9690,9785,9883,9971,10077,10177,10280,10408,10493,10607", "endColumns": "118,115,118,117,101,94,111,137,115,145,83,99,91,98,117,123,104,136,133,143,188,137,122,123,125,92,95,124,140,94,110,108,138,144,110,98,76,93,93,87,82,104,85,82,98,100,94,97,87,105,99,102,127,84,113,106", "endOffsets": "4612,4728,4847,4965,5067,5162,5274,5412,5528,5674,5758,5858,5950,6049,6167,6291,6396,6533,6667,6811,7000,7138,7261,7385,7511,7604,7700,7825,7966,8061,8172,8281,8420,8565,8676,8775,8852,8946,9040,9128,9211,9316,9402,9485,9584,9685,9780,9878,9966,10072,10172,10275,10403,10488,10602,10709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "4,5,6,7,8,9,10,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "343,442,544,642,739,847,958,11283", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "437,539,637,734,842,953,1075,11379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c08faea6ded0ab196312fa217ce48810\\transformed\\credentials-1.2.0-rc01\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,122", "endOffsets": "165,288"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,220", "endColumns": "114,122", "endOffsets": "215,338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\481238e97b83b818931a2722abe3cf6f\\transformed\\foundation-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11648,11746", "endColumns": "97,98", "endOffsets": "11741,11840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e262177e497b967ef733f0fbd8d750b5\\transformed\\browser-1.4.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3599,4011,4112,4223", "endColumns": "114,100,110,100", "endOffsets": "3709,4107,4218,4319"}}]}]}