{"logs": [{"outputFile": "com.dev.aa103_poc.test.app-mergeDebugAndroidTestResources-1:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\abd85caff3ff8eef039557ffb8e6d4a5\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,408,509,614,719,2023", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "200,302,403,504,609,714,827,2119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\48127b829acf9e3a2697ec3974b843b9\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,980,1051,1136,1224,1296,1376,1446", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,975,1046,1131,1219,1291,1371,1441,1564"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,925,1009,1111,1212,1296,1378,1467,1555,1637,1707,1778,1863,1951,2124,2204,2274", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "920,1004,1106,1207,1291,1373,1462,1550,1632,1702,1773,1858,1946,2018,2199,2269,2392"}}]}]}