package com.dev.aa103_poc.ui.createproject

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.dev.aa103_poc.data.model.Project
import com.dev.aa103_poc.data.repository.ProjectRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * UI state for the Create Project screen
 */
data class CreateProjectUiState(
    val title: String = "",
    val selectedType: String = Project.TYPE_WALL,
    val selectedJobType: String = Project.JOB_TYPE_PAINTING,
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val isProjectCreated: Boolean = false,
    val createdProjectId: String? = null
)

/**
 * ViewModel for the Create Project screen
 * Handles project creation logic and state management
 */
class CreateProjectViewModel(
    private val projectRepository: ProjectRepository = ProjectRepository()
) : ViewModel() {
    
    companion object {
        private const val TAG = "CreateProjectViewModel"
    }
    
    private val _uiState = MutableStateFlow(CreateProjectUiState())
    val uiState: StateFlow<CreateProjectUiState> = _uiState.asStateFlow()
    
    /**
     * Update the project title
     */
    fun updateTitle(title: String) {
        _uiState.value = _uiState.value.copy(
            title = title,
            errorMessage = null // Clear error when user types
        )
    }
    
    /**
     * Update the selected project type
     */
    fun updateType(type: String) {
        _uiState.value = _uiState.value.copy(
            selectedType = type,
            errorMessage = null
        )
    }
    
    /**
     * Update the selected job type
     */
    fun updateJobType(jobType: String) {
        _uiState.value = _uiState.value.copy(
            selectedJobType = jobType,
            errorMessage = null
        )
    }
    
    /**
     * Clear any error messages
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * Validate the form inputs
     */
    private fun validateInputs(): Boolean {
        val currentState = _uiState.value
        
        if (currentState.title.trim().isEmpty()) {
            _uiState.value = currentState.copy(
                errorMessage = "Project title is required"
            )
            return false
        }
        
        if (currentState.title.trim().length > 200) {
            _uiState.value = currentState.copy(
                errorMessage = "Project title must be 200 characters or less"
            )
            return false
        }
        
        return true
    }
    
    /**
     * Create a new project
     */
    fun createProject() {
        if (!validateInputs()) return
        
        val currentState = _uiState.value
        _uiState.value = currentState.copy(isLoading = true, errorMessage = null)
        
        viewModelScope.launch {
            try {
                val projectId = projectRepository.createProject(
                    title = currentState.title.trim(),
                    type = currentState.selectedType,
                    jobType = currentState.selectedJobType
                )
                
                Log.d(TAG, "Project created successfully with ID: $projectId")
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isProjectCreated = true,
                    createdProjectId = projectId,
                    errorMessage = null
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating project", e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "Failed to create project: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Reset the creation state (useful when navigating back)
     */
    fun resetCreationState() {
        _uiState.value = _uiState.value.copy(
            isProjectCreated = false,
            createdProjectId = null
        )
    }
}
