package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.dev.aa103_poc.AA103Application",
    rootPackage = "com.dev.aa103_poc",
    originatingRoot = "com.dev.aa103_poc.AA103Application",
    originatingRootPackage = "com.dev.aa103_poc",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "AA103Application",
    originatingRootSimpleNames = "AA103Application"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_dev_aa103_poc_AA103Application {
}
