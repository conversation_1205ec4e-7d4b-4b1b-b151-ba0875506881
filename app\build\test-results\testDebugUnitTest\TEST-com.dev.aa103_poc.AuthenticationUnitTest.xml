<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.dev.aa103_poc.AuthenticationUnitTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-09-01T21:36:10" hostname="DESKTOP-R5PBK2U" time="0.009">
  <properties/>
  <testcase name="signInUiState_withSignedInState_updatesCorrectly" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.002"/>
  <testcase name="signInUiState_initialState_isCorrect" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.001"/>
  <testcase name="emailValidation_invalidEmail_returnsFalse" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.001"/>
  <testcase name="signInUiState_togglePasswordVisibility_updatesCorrectly" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.001"/>
  <testcase name="signInUiState_withLoadingState_updatesCorrectly" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.0"/>
  <testcase name="emailValidation_validEmail_returnsTrue" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.0"/>
  <testcase name="signInUiState_withPassword_updatesCorrectly" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.001"/>
  <testcase name="signInUiState_withErrorMessage_updatesCorrectly" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.0"/>
  <testcase name="signInUiState_withEmail_updatesCorrectly" classname="com.dev.aa103_poc.AuthenticationUnitTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
