package com.dev.aa103_poc.data.repository

import com.dev.aa103_poc.data.model.ProjectStream
import kotlinx.coroutines.flow.Flow

/**
 * Interface for project repository operations
 * Defines contract for loading project data with manual refresh
 */
interface ProjectRepositoryInterface {
    /**
     * Load projects for a specific user from cache
     * @param uid The user ID to load projects for
     * @return ProjectStream with cached data
     */
    suspend fun loadFromCache(uid: String): ProjectStream

    /**
     * Refresh projects for a specific user from server
     * @param uid The user ID to refresh projects for
     * @return ProjectStream with fresh server data
     */
    suspend fun refreshFromServer(uid: String): ProjectStream
}
