{"logs": [{"outputFile": "com.dev.aa103_poc.app-mergeReleaseResources-3:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\481238e97b83b818931a2722abe3cf6f\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "107,108", "startColumns": "4,4", "startOffsets": "11232,11318", "endColumns": "85,84", "endOffsets": "11313,11398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6fa2c1a7648815a2389f1e233b858239\\transformed\\material3-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,391,502,599,695,808,937,1058,1189,1274,1374,1464,1564,1682,1802,1907,2034,2159,2289,2437,2558,2672,2791,2903,2994,3093,3206,3331,3425,3541,3647,3774,3908,4018,4115,4195,4293,4389,4475,4561,4666,4752,4839,4942,5044,5139,5242,5328,5429,5527,5629,5756,5842,5942", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "164,276,386,497,594,690,803,932,1053,1184,1269,1369,1459,1559,1677,1797,1902,2029,2154,2284,2432,2553,2667,2786,2898,2989,3088,3201,3326,3420,3536,3642,3769,3903,4013,4110,4190,4288,4384,4470,4556,4661,4747,4834,4937,5039,5134,5237,5323,5424,5522,5624,5751,5837,5937,6032"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4346,4460,4572,4682,4793,4890,4986,5099,5228,5349,5480,5565,5665,5755,5855,5973,6093,6198,6325,6450,6580,6728,6849,6963,7082,7194,7285,7384,7497,7622,7716,7832,7938,8065,8199,8309,8406,8486,8584,8680,8766,8852,8957,9043,9130,9233,9335,9430,9533,9619,9720,9818,9920,10047,10133,10233", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "4455,4567,4677,4788,4885,4981,5094,5223,5344,5475,5560,5660,5750,5850,5968,6088,6193,6320,6445,6575,6723,6844,6958,7077,7189,7280,7379,7492,7617,7711,7827,7933,8060,8194,8304,8401,8481,8579,8675,8761,8847,8952,9038,9125,9228,9330,9425,9528,9614,9715,9813,9915,10042,10128,10228,10323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\d500cfbfd366bfa495bb2611edc98d15\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "11,12,32,33,34,38,39,96,97,98,99,100,101,102,104,105,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1039,1129,3558,3667,3765,4167,4256,10328,10414,10497,10562,10628,10708,10792,10967,11045,11111", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "1124,1201,3662,3760,3849,4251,4341,10409,10492,10557,10623,10703,10787,10861,11040,11106,11227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\e262177e497b967ef733f0fbd8d750b5\\transformed\\browser-1.4.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,99", "endOffsets": "148,246,361,461"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3460,3854,3952,4067", "endColumns": "97,97,114,99", "endOffsets": "3553,3947,4062,4162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\799b421d1a1acf7bdf6b855ee1ea22a8\\transformed\\play-services-base-18.0.1\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1206,1314,1484,1614,1721,1873,1998,2106,2354,2506,2611,2777,2906,3077,3238,3306,3375", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "1309,1479,1609,1716,1868,1993,2101,2217,2501,2606,2772,2901,3072,3233,3301,3370,3455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc3f110c8971a71c5a78795c57b75cdc\\transformed\\play-services-basement-18.4.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2222", "endColumns": "131", "endOffsets": "2349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8e2933c94d70716d0d033357ed075e2d\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "4,5,6,7,8,9,10,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "333,429,532,631,729,830,928,10866", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "424,527,626,724,825,923,1034,10962"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c08faea6ded0ab196312fa217ce48810\\transformed\\credentials-1.2.0-rc01\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,114", "endOffsets": "163,278"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,218", "endColumns": "112,114", "endOffsets": "213,328"}}]}]}