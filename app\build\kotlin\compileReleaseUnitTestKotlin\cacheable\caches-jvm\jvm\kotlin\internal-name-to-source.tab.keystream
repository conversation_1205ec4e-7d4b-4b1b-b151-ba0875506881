(com/dev/aa103_poc/AuthenticationUnitTest&com/dev/aa103_poc/ui/auth/AuthGateTest7com/dev/aa103_poc/ui/auth/AuthGateTest$MockFirebaseUser3com/dev/aa103_poc/ui/projects/ProjectsViewModelTesticom/dev/aa103_poc/ui/projects/ProjectsViewModelTest$when user is not signed in, should show error state$1hcom/dev/aa103_poc/ui/projects/ProjectsViewModelTest$when user has no projects, should show empty state$1gcom/dev/aa103_poc/ui/projects/ProjectsViewModelTest$when user has projects, should show content state$1/com/dev/aa103_poc/ui/signin/SignInViewModelTest                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         